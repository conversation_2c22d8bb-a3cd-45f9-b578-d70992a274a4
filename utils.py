import numpy as np
import cv2
import rasterio
from pyproj import Proj, transform
import json
import matplotlib.pyplot as plt
from skimage.measure import approximate_polygon
import requests
import math
from PIL import Image
import io

def show_mask(mask, ax, random_color=False):
    """
    Display a mask on a matplotlib axis.

    Args:
        mask: The mask to display
        ax: The matplotlib axis
        random_color: Whether to use a random color
    """
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)

def show_points(coords, labels, ax, marker_size=375):
    """
    Display points on a matplotlib axis.

    Args:
        coords: The coordinates of the points
        labels: The labels of the points
        ax: The matplotlib axis
        marker_size: The size of the markers
    """
    pos_points = coords[labels==1]
    neg_points = coords[labels==0]
    ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)
    ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)

def show_box(box, ax):
    """
    Display a bounding box on a matplotlib axis.

    Args:
        box: The bounding box coordinates [x0, y0, x1, y1]
        ax: The matplotlib axis
    """
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))


def latlon_to_pixel(lat, lon, image_width, image_height, min_lat, max_lat, min_lon, max_lon):
    """
    Convert latitude and longitude to pixel coordinates.

    Args:
        lat: Latitude
        lon: Longitude
        image_width: Width of the image
        image_height: Height of the image
        min_lat: Minimum latitude
        max_lat: Maximum latitude
        min_lon: Minimum longitude
        max_lon: Maximum longitude

    Returns:
        Pixel coordinates (x, y)
    """
    x = int(((lon - min_lon) / (max_lon - min_lon)) * image_width)
    y = int(((max_lat - lat) / (max_lat - min_lat)) * image_height)
    return x, y

def geographic_to_epsg4326(lon1, lat1):
    """
    Convert geographic coordinates to EPSG:4326.

    Args:
        lon1: Longitude
        lat1: Latitude

    Returns:
        Coordinates in EPSG:4326 (lon_4326, lat_4326)
    """
    source_crs = Proj(init='epsg:3857')
    target_crs = Proj(init='epsg:4326')
    lon_4326, lat_4326 = transform(source_crs, target_crs, lon1, lat1)
    return lon_4326, lat_4326

def save_contour_points(contour_points, output_file='contour_points.json'):
    """
    Save contour points to a GeoJSON file.

    Args:
        contour_points: List of contour points
        output_file: Path to the output file
    """
    polygon = {
        "type": "Polygon",
        "coordinates": [contour_points]
    }
    with open(output_file, 'w') as file:
        json.dump(polygon, file)

def deg2num(lat_deg, lon_deg, zoom):
    """Convert latitude/longitude to tile numbers"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def num2deg(xtile, ytile, zoom):
    """Convert tile numbers to latitude/longitude"""
    n = 2.0 ** zoom
    lon_deg = xtile / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def download_satellite_image(center_lat, center_lon, zoom_level):
    """
    Download a satellite image for a given location using Esri World Imagery tiles.

    Args:
        center_lat: Center latitude
        center_lon: Center longitude
        zoom_level: Zoom level
        output_file: Path to the output file

    Returns:
        Dictionary with image path and bounding box information
    """
    try:
        # Get tile coordinates for the center point
        center_x, center_y = deg2num(center_lat, center_lon, zoom_level)
        
        # Download a 3x3 grid of tiles centered on the point
        tile_size = 256
        grid_size = 3
        offset = grid_size // 2
        
        # Create a larger image to hold all tiles
        full_image = Image.new('RGB', (tile_size * grid_size, tile_size * grid_size))
        
        # Calculate the actual bounding box
        top_left_x = center_x - offset
        top_left_y = center_y - offset
        bottom_right_x = center_x + offset + 1
        bottom_right_y = center_y + offset + 1
        
        # Get geographic bounds
        top_left_lat, top_left_lon = num2deg(top_left_x, top_left_y, zoom_level)
        bottom_right_lat, bottom_right_lon = num2deg(bottom_right_x, bottom_right_y, zoom_level)
        
        # Download tiles
        for i in range(grid_size):
            for j in range(grid_size):
                tile_x = center_x - offset + i
                tile_y = center_y - offset + j
                
                # Use Esri World Imagery (satellite tiles) - free to use with attribution
                # Alternative: Google Satellite tiles (may require API key for production)
                url = f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom_level}/{tile_y}/{tile_x}"
                
                try:
                    headers = {
                        'User-Agent': 'Field Boundary Detection API/1.0'
                    }
                    response = requests.get(url, timeout=15, headers=headers)
                    response.raise_for_status()
                    
                    if response.content:
                        tile_image = Image.open(io.BytesIO(response.content))
                        full_image.paste(tile_image, (i * tile_size, j * tile_size))
                        print(f"Downloaded tile {tile_x},{tile_y}")
                    else:
                        print(f"Warning: Empty response for tile {tile_x},{tile_y}")
                        blank_tile = Image.new('RGB', (tile_size, tile_size), color=(128, 128, 128))
                        full_image.paste(blank_tile, (i * tile_size, j * tile_size))
                    
                except requests.RequestException as e:
                    print(f"Warning: Failed to download tile {tile_x},{tile_y}: {e}")
                    # Create a blank tile if download fails
                    blank_tile = Image.new('RGB', (tile_size, tile_size), color=(128, 128, 128))
                    full_image.paste(blank_tile, (i * tile_size, j * tile_size))
        
        # Convert PIL image to numpy array
        image_array = np.array(full_image)

        # Calculate the bounding box in geographic coordinates
        bbox = {
            'xmin': top_left_lon,
            'xmax': bottom_right_lon,
            'ymin': bottom_right_lat,  # Note: bottom_right_lat is actually smaller (south)
            'ymax': top_left_lat       # Note: top_left_lat is actually larger (north)
        }

        return {
            'bbox': bbox,
            'image_array': image_array,
            'width': full_image.width,
            'height': full_image.height
        }
        
    except Exception as e:
        raise Exception(f"Failed to download satellite image: {str(e)}")
