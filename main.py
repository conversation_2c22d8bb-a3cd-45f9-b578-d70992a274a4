from flask import Flask, request, jsonify, send_file
import os
import cv2
import numpy as np
import rasterio
from utils import download_satellite_image
from dotenv import load_dotenv
from datetime import datetime

# Vertex AI imports (lazy-optional with fallbacks)
# Try import aiplatform
try:
    from google.cloud import aiplatform
except Exception:
    aiplatform = None

# Try import vertexai init
try:
    from vertexai import init as vertexai_init
except Exception:
    vertexai_init = None

# Try preview vision models first, then stable
SEGMENTER_CLASS = None
VertexImage = None
_import_errors = []
try:
    from vertexai.preview.vision_models import ImageSegmenterModel as SEGMENTER_CLASS, Image as VertexImage
except Exception as _e1:
    _import_errors.append(str(_e1))
    try:
        from vertexai.vision_models import ImageSegmenter as SEGMENTER_CLASS, Image as VertexImage
    except Exception as _e2:
        _import_errors.append(str(_e2))
        SEGMENTER_CLASS = None
        VertexImage = None

app = Flask(__name__)
# --- Vertex AI helpers ---

def ensure_segmenter():
    """Ensure the global Vertex AI ImageSegmenter is initialized."""
    global segmenter, PROJECT_ID, LOCATION
    if segmenter is not None:
        return segmenter

    load_dotenv()
    PROJECT_ID = PROJECT_ID or os.getenv('GCP_PROJECT') or os.getenv('GOOGLE_CLOUD_PROJECT')
    LOCATION = LOCATION or os.getenv('GCP_LOCATION') or os.getenv('VERTEX_LOCATION') or 'us-central1'
    keyfile = os.getenv('GOOGLE_APPLICATION_CREDENTIALS') or 'keyfile.json'
    if keyfile and os.path.exists(keyfile):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = keyfile

    if aiplatform is None or vertexai_init is None or SEGMENTER_CLASS is None or VertexImage is None:
        raise RuntimeError("Vertex AI SDK not available. Install google-cloud-aiplatform and configure .env. "
                           f"Import errors: {_import_errors}")

    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    vertexai_init(project=PROJECT_ID, location=LOCATION)
    try:
        segmenter = SEGMENTER_CLASS.from_pretrained('sam')
    except Exception:
        segmenter = SEGMENTER_CLASS.from_pretrained('image-segmentation')
    return segmenter


def segment_with_vertex(image_rgb: np.ndarray, x: int, y: int):
    """Run Vertex AI SAM on an RGB image array with a single positive point.

    Returns: (masks_list, scores_array, temp_input_path)
    """
    seg = ensure_segmenter()

    # Save temp input image (PNG)
    os.makedirs('temp_images', exist_ok=True)
    ts = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
    temp_input_path = os.path.join('temp_images', f'input_{ts}.png')
    cv2.imwrite(temp_input_path, cv2.cvtColor(image_rgb, cv2.COLOR_RGB2BGR))

    # Prepare Vertex image
    try:
        vimage = VertexImage.load_from_file(temp_input_path)
    except Exception:
        # Some SDKs use from_file
        vimage = VertexImage.from_file(temp_input_path)

    # Call segmentation
    try:
        response = seg.segment(
            image=vimage,
            prompt_points=[(int(x), int(y))],
            prompt_labels=[1],  # 1 = foreground
        )
    except TypeError:
        # Some SDK versions use singular argument names
        response = seg.segment(
            image=vimage,
            prompt_point=[(int(x), int(y))],
            prompt_label=[1],
        )

    # Extract masks and scores from response
    masks = None
    scores = None
    if hasattr(response, 'masks'):
        masks = response.masks
    elif isinstance(response, dict) and 'masks' in response:
        masks = response['masks']

    if hasattr(response, 'confidences'):
        scores = response.confidences
    elif hasattr(response, 'scores'):
        scores = response.scores
    elif isinstance(response, dict) and 'scores' in response:
        scores = response['scores']

    if masks is None:
        raise RuntimeError('Vertex AI did not return any masks')

    masks_np = []
    for m in masks:
        if isinstance(m, np.ndarray):
            masks_np.append(m.astype(bool))
        elif hasattr(m, 'numpy'):
            masks_np.append(m.numpy().astype(bool))
        else:
            try:
                # Try to convert from PIL-like or list
                import numpy as _np
                masks_np.append(_np.array(m).astype(bool))
            except Exception:
                pass

    if not masks_np:
        raise RuntimeError('Unable to parse masks from Vertex AI response')

    if scores is None:
        scores = [1.0] * len(masks_np)

    return masks_np, np.array(scores, dtype=float), temp_input_path


# Vertex AI globals
segmenter = None
PROJECT_ID = None
LOCATION = None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})

@app.route('/initialize', methods=['POST'])
def initialize_model():
    """Initialize Vertex AI SAM using environment variables or request overrides"""
    global segmenter, PROJECT_ID, LOCATION

    try:
        load_dotenv()
        data = request.json or {}

        # Read configuration from .env or request
        PROJECT_ID = data.get('project') or os.getenv('GCP_PROJECT') or os.getenv('GOOGLE_CLOUD_PROJECT')
        LOCATION = data.get('location') or os.getenv('GCP_LOCATION') or os.getenv('VERTEX_LOCATION') or 'us-central1'
        keyfile = data.get('keyfile') or os.getenv('GOOGLE_APPLICATION_CREDENTIALS') or 'keyfile.json'

        if keyfile and os.path.exists(keyfile):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = keyfile

        if aiplatform is None or vertexai_init is None or SEGMENTER_CLASS is None or VertexImage is None:
            return jsonify({
                "error": "Vertex AI SDK not available. Install google-cloud-aiplatform and ensure 'vertexai' is importable.",
                "import_errors": _import_errors
            }), 500

        # Initialize Vertex AI
        aiplatform.init(project=PROJECT_ID, location=LOCATION)
        vertexai_init(project=PROJECT_ID, location=LOCATION)

        # Load SAM model on Vertex AI
        try:
            segmenter = SEGMENTER_CLASS.from_pretrained('sam')
        except Exception:
            # Fallback name if 'sam' alias is not available in this SDK version
            segmenter = SEGMENTER_CLASS.from_pretrained('image-segmentation')

        return jsonify({
            "status": "Vertex AI initialized",
            "project": PROJECT_ID,
            "location": LOCATION
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/segment-tiff', methods=['POST'])
def segment_tiff():
    """Segment a field boundary from a TIFF file"""
    global predictor

    try:
        # Ensure Vertex AI segmenter is initialized
        try:
            ensure_segmenter()
        except Exception as init_err:
            return jsonify({"error": f"Vertex AI not initialized: {str(init_err)}"}), 500

        # Get the request data
        data = request.json
        image_path = data.get('image_path')
        x = data.get('x')
        y = data.get('y')

        # Validate required parameters
        if not image_path:
            return jsonify({"error": "Image path is required"}), 400
        if x is None or y is None:
            return jsonify({"error": "Point coordinates (x, y) are required"}), 400

        # Check if the image exists
        if not os.path.exists(image_path):
            return jsonify({"error": f"Image file {image_path} not found"}), 400

        # Process the TIFF file
        with rasterio.open(image_path) as src:
            # Get the image metadata
            transform = src.transform

            # Read the image data
            image_data = src.read()

            # Get the image dimensions
            height, width = image_data.shape[1], image_data.shape[2]

            # Check if the coordinates are within the image bounds
            if x < 0 or x >= width or y < 0 or y >= height:
                return jsonify({"error": f"Coordinates ({x}, {y}) are outside image bounds (0-{width-1}, 0-{height-1})"}), 400



            # Prepare the image for SAM
            if len(image_data.shape) == 3 and image_data.shape[0] >= 3:
                # Multi-band image - use first 3 bands as RGB
                image_rgb = np.transpose(image_data[:3], (1, 2, 0))
                # Normalize to 0-255 range
                image_rgb = ((image_rgb - image_rgb.min()) / (image_rgb.max() - image_rgb.min()) * 255).astype(np.uint8)
            else:
                # Single band - convert to RGB
                if len(image_data.shape) == 3:
                    single_band = image_data[0]
                else:
                    single_band = image_data
                # Normalize to 0-255 range
                normalized = ((single_band - single_band.min()) / (single_band.max() - single_band.min()) * 255).astype(np.uint8)
                image_rgb = np.stack([normalized, normalized, normalized], axis=2)

        # Run segmentation on Vertex AI SAM
        masks, scores, temp_input_path = segment_with_vertex(image_rgb, int(x), int(y))

        # Use the mask with the highest score
        best_mask_idx = np.argmax(scores)
        mask = masks[best_mask_idx]

        # Convert mask to binary image
        mask_uint8 = (mask * 255).astype(np.uint8)

        # Find contours in the binary mask
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Convert contours to list of points in both pixel and geographic coordinates
        contour_points_pixel = []
        contour_points_geo = []

        if contours:
            # Use the largest contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Simplify the contour
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

            # Extract points from the simplified contour
            for point in simplified_contour:
                px, py = point[0]
                contour_points_pixel.append([int(px), int(py)])

                # Convert pixel coordinates to geographic coordinates
                lon_geo, lat_geo = rasterio.transform.xy(transform, py, px)
                contour_points_geo.append([float(lon_geo), float(lat_geo)])

        # Save mask and overlay images
        os.makedirs('temp_images', exist_ok=True)
        ts = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
        mask_path = os.path.join('temp_images', f'mask_{ts}.png')
        cv2.imwrite(mask_path, mask_uint8)
        overlay_path = None
        if contours:
            overlay = image_rgb.copy()
            cv2.polylines(overlay, [simplified_contour], isClosed=True, color=(255, 0, 0), thickness=2)
            overlay_path = os.path.join('temp_images', f'overlay_{ts}.png')
            cv2.imwrite(overlay_path, cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))

        # Create GeoJSON directly with output file paths
        geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "field_boundary": True,
                        "segmentation_score": float(scores[best_mask_idx]),
                        "outputs": {
                            "input_png": temp_input_path,
                            "mask_png": mask_path,
                            "overlay_png": overlay_path
                        }
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [contour_points_geo + [contour_points_geo[0]]] if contour_points_geo else []
                    }
                }
            ]
        }

        # Return clean GeoJSON format directly
        return jsonify(geojson)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/get-image/<path:filename>', methods=['GET'])
def get_image(filename):
    """Return an image file"""
    try:
        return send_file(filename, mimetype='image/png')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/get-geojson/<path:filename>', methods=['GET'])
def get_geojson(filename):
    """Return a GeoJSON file"""
    try:
        return send_file(filename, mimetype='application/json')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/view-image/<path:filename>', methods=['GET'])
def view_image(filename):
    """View an image file in the browser"""
    try:
        if not os.path.exists(filename):
            return jsonify({"error": f"Image file {filename} not found"}), 404
        return send_file(filename, mimetype='image/png')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/segment-coordinates', methods=['POST'])
def segment_coordinates():
    """Segment a field boundary from latitude/longitude coordinates by downloading satellite imagery"""
    global predictor

    try:
        # Ensure Vertex AI segmenter is initialized
        try:
            ensure_segmenter()
        except Exception as init_err:
            return jsonify({"error": f"Vertex AI not initialized: {str(init_err)}"}), 500

        # Get the request data
        data = request.json
        latitude = data.get('latitude')
        longitude = data.get('longitude')
        zoom_level = data.get('zoom_level', 16)  # Default zoom level
        tile_source = data.get('tile_source', 'Satellite')  # Default tile source

        # Validate required parameters
        if latitude is None:
            return jsonify({"error": "Latitude is required"}), 400
        if longitude is None:
            return jsonify({"error": "Longitude is required"}), 400

        # Validate parameter types and ranges
        try:
            latitude = float(latitude)
            longitude = float(longitude)
            zoom_level = int(zoom_level)
        except (ValueError, TypeError):
            return jsonify({"error": "Invalid parameter types. Latitude and longitude must be numbers, zoom_level must be an integer"}), 400

        if not (-90 <= latitude <= 90):
            return jsonify({"error": "Latitude must be between -90 and 90"}), 400
        if not (-180 <= longitude <= 180):
            return jsonify({"error": "Longitude must be between -180 and 180"}), 400
        if not (1 <= zoom_level <= 20):
            return jsonify({"error": "Zoom level must be between 1 and 20"}), 400

        # Download satellite image
        try:
            download_result = download_satellite_image(latitude, longitude, zoom_level)
            bbox = download_result['bbox']
            width = download_result['width']
            height = download_result['height']
            image_rgb = download_result['image_array']
        except Exception as e:
            return jsonify({"error": f"Failed to download satellite image: {str(e)}"}), 500

        # Convert lat/lon to pixel coordinates within the downloaded tile
        # Use the bounding box from the download to calculate pixel position
        x_pixel = int(((longitude - bbox['xmin']) / (bbox['xmax'] - bbox['xmin'])) * width)
        y_pixel = int(((bbox['ymax'] - latitude) / (bbox['ymax'] - bbox['ymin'])) * height)

        # Check if the coordinates are within the image bounds
        if x_pixel < 0 or x_pixel >= width or y_pixel < 0 or y_pixel >= height:
            return jsonify({"error": f"Coordinates ({latitude}, {longitude}) fall outside the downloaded tile bounds"}), 400



        # Run segmentation on Vertex AI SAM
        masks, scores, temp_input_path = segment_with_vertex(image_rgb, int(x_pixel), int(y_pixel))

        # Use the mask with the highest score
        best_mask_idx = np.argmax(scores)
        mask = masks[best_mask_idx]

        # Convert mask to binary image
        mask_uint8 = (mask * 255).astype(np.uint8)

        # Find contours in the binary mask
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Convert contours to list of points in both pixel and geographic coordinates
        contour_points_pixel = []
        contour_points_geo = []

        if contours:
            # Use the largest contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Simplify the contour
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

            # Extract points from the simplified contour
            for point in simplified_contour:
                px, py = point[0]
                contour_points_pixel.append([int(px), int(py)])

                # Convert pixel coordinates to geographic coordinates
                lon_geo = bbox['xmin'] + (px / width) * (bbox['xmax'] - bbox['xmin'])
                lat_geo = bbox['ymax'] - (py / height) * (bbox['ymax'] - bbox['ymin'])
                contour_points_geo.append([float(lon_geo), float(lat_geo)])

        # Save mask and overlay images
        os.makedirs('temp_images', exist_ok=True)
        ts = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
        mask_path = os.path.join('temp_images', f'mask_{ts}.png')
        cv2.imwrite(mask_path, mask_uint8)
        overlay_path = None
        if contours:
            overlay = image_rgb.copy()
            cv2.polylines(overlay, [simplified_contour], isClosed=True, color=(255, 0, 0), thickness=2)
            overlay_path = os.path.join('temp_images', f'overlay_{ts}.png')
            cv2.imwrite(overlay_path, cv2.cvtColor(overlay, cv2.COLOR_RGB2BGR))

        # Create GeoJSON directly with output file paths
        geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "field_boundary": True,
                        "source_coordinates": {
                            "latitude": latitude,
                            "longitude": longitude
                        },
                        "zoom_level": zoom_level,
                        "tile_source": tile_source,
                        "segmentation_score": float(scores[best_mask_idx]),
                        "outputs": {
                            "input_png": temp_input_path,
                            "mask_png": mask_path,
                            "overlay_png": overlay_path
                        }
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [contour_points_geo + [contour_points_geo[0]]] if contour_points_geo else []
                    }
                }
            ]
        }

        # Return clean GeoJSON format directly
        return jsonify(geojson)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
