from flask import Flask, request, jsonify, send_file
import os
import cv2
import numpy as np
import rasterio
from segment_anything import sam_model_registry, SamPredictor
import torch
from utils import download_satellite_image

app = Flask(__name__)

# Check if CUDA is available, otherwise use CPU
device = "cuda" if torch.cuda.is_available() else "cpu"

# Global variable to store the SAM predictor
predictor = None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})

@app.route('/initialize', methods=['POST'])
def initialize_model():
    """Initialize the SAM model with the provided checkpoint"""
    global predictor

    data = request.json
    sam_checkpoint = data.get('sam_checkpoint', 'sam_vit_h_4b8939.pth')
    model_type = data.get('model_type', 'vit_h')

    # Check if the checkpoint file exists
    if not os.path.exists(sam_checkpoint):
        return jsonify({"error": f"Checkpoint file {sam_checkpoint} not found"}), 400

    try:
        # Initialize the SAM model
        sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
        sam.to(device=device)

        # Create the predictor
        predictor = SamPredictor(sam)

        return jsonify({
            "status": "Model initialized successfully",
            "device": device,
            "model_type": model_type,
            "checkpoint": sam_checkpoint
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/segment-tiff', methods=['POST'])
def segment_tiff():
    """Segment a field boundary from a TIFF file"""
    global predictor

    try:
        # Check if the model is initialized
        if predictor is None:
            return jsonify({"error": "Model not initialized. Call /initialize first."}), 400

        # Get the request data
        data = request.json
        image_path = data.get('image_path')
        x = data.get('x')
        y = data.get('y')

        # Validate required parameters
        if not image_path:
            return jsonify({"error": "Image path is required"}), 400
        if x is None or y is None:
            return jsonify({"error": "Point coordinates (x, y) are required"}), 400

        # Check if the image exists
        if not os.path.exists(image_path):
            return jsonify({"error": f"Image file {image_path} not found"}), 400

        # Process the TIFF file
        with rasterio.open(image_path) as src:
            # Get the image metadata
            transform = src.transform

            # Read the image data
            image_data = src.read()

            # Get the image dimensions
            height, width = image_data.shape[1], image_data.shape[2]

            # Check if the coordinates are within the image bounds
            if x < 0 or x >= width or y < 0 or y >= height:
                return jsonify({"error": f"Coordinates ({x}, {y}) are outside image bounds (0-{width-1}, 0-{height-1})"}), 400



            # Prepare the image for SAM
            if len(image_data.shape) == 3 and image_data.shape[0] >= 3:
                # Multi-band image - use first 3 bands as RGB
                image_rgb = np.transpose(image_data[:3], (1, 2, 0))
                # Normalize to 0-255 range
                image_rgb = ((image_rgb - image_rgb.min()) / (image_rgb.max() - image_rgb.min()) * 255).astype(np.uint8)
            else:
                # Single band - convert to RGB
                if len(image_data.shape) == 3:
                    single_band = image_data[0]
                else:
                    single_band = image_data
                # Normalize to 0-255 range
                normalized = ((single_band - single_band.min()) / (single_band.max() - single_band.min()) * 255).astype(np.uint8)
                image_rgb = np.stack([normalized, normalized, normalized], axis=2)

        # Set the image for the SAM predictor
        predictor.set_image(image_rgb)

        # Perform segmentation with SAM
        input_point = np.array([[x, y]])
        input_label = np.array([1])  # 1 for foreground

        # Get masks from SAM
        masks, scores, _ = predictor.predict(
            point_coords=input_point,
            point_labels=input_label,
            multimask_output=True,
        )

        # Use the mask with the highest score
        best_mask_idx = np.argmax(scores)
        mask = masks[best_mask_idx]

        # Convert mask to binary image
        mask_uint8 = (mask * 255).astype(np.uint8)

        # Find contours in the binary mask
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Convert contours to list of points in both pixel and geographic coordinates
        contour_points_pixel = []
        contour_points_geo = []

        if contours:
            # Use the largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Simplify the contour
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)
            
            # Extract points from the simplified contour
            for point in simplified_contour:
                px, py = point[0]
                contour_points_pixel.append([int(px), int(py)])
                
                # Convert pixel coordinates to geographic coordinates
                lon_geo, lat_geo = rasterio.transform.xy(transform, py, px)
                contour_points_geo.append([float(lon_geo), float(lat_geo)])

        # Create GeoJSON directly without saving files
        geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "field_boundary": True,
                        "segmentation_score": float(scores[best_mask_idx])
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [contour_points_geo + [contour_points_geo[0]]] if contour_points_geo else []
                    }
                }
            ]
        }

        # Return clean GeoJSON format directly
        return jsonify(geojson)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/get-image/<path:filename>', methods=['GET'])
def get_image(filename):
    """Return an image file"""
    try:
        return send_file(filename, mimetype='image/png')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/get-geojson/<path:filename>', methods=['GET'])
def get_geojson(filename):
    """Return a GeoJSON file"""
    try:
        return send_file(filename, mimetype='application/json')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/view-image/<path:filename>', methods=['GET'])
def view_image(filename):
    """View an image file in the browser"""
    try:
        if not os.path.exists(filename):
            return jsonify({"error": f"Image file {filename} not found"}), 404
        return send_file(filename, mimetype='image/png')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/segment-coordinates', methods=['POST'])
def segment_coordinates():
    """Segment a field boundary from latitude/longitude coordinates by downloading satellite imagery"""
    global predictor

    try:
        # Check if the model is initialized
        if predictor is None:
            return jsonify({"error": "Model not initialized. Call /initialize first."}), 400

        # Get the request data
        data = request.json
        latitude = data.get('latitude')
        longitude = data.get('longitude')
        zoom_level = data.get('zoom_level', 16)  # Default zoom level
        tile_source = data.get('tile_source', 'Satellite')  # Default tile source

        # Validate required parameters
        if latitude is None:
            return jsonify({"error": "Latitude is required"}), 400
        if longitude is None:
            return jsonify({"error": "Longitude is required"}), 400

        # Validate parameter types and ranges
        try:
            latitude = float(latitude)
            longitude = float(longitude)
            zoom_level = int(zoom_level)
        except (ValueError, TypeError):
            return jsonify({"error": "Invalid parameter types. Latitude and longitude must be numbers, zoom_level must be an integer"}), 400

        if not (-90 <= latitude <= 90):
            return jsonify({"error": "Latitude must be between -90 and 90"}), 400
        if not (-180 <= longitude <= 180):
            return jsonify({"error": "Longitude must be between -180 and 180"}), 400
        if not (1 <= zoom_level <= 20):
            return jsonify({"error": "Zoom level must be between 1 and 20"}), 400

        # Download satellite image
        try:
            download_result = download_satellite_image(latitude, longitude, zoom_level)
            bbox = download_result['bbox']
            width = download_result['width']
            height = download_result['height']
            image_rgb = download_result['image_array']
        except Exception as e:
            return jsonify({"error": f"Failed to download satellite image: {str(e)}"}), 500

        # Convert lat/lon to pixel coordinates within the downloaded tile
        # Use the bounding box from the download to calculate pixel position
        x_pixel = int(((longitude - bbox['xmin']) / (bbox['xmax'] - bbox['xmin'])) * width)
        y_pixel = int(((bbox['ymax'] - latitude) / (bbox['ymax'] - bbox['ymin'])) * height)

        # Check if the coordinates are within the image bounds
        if x_pixel < 0 or x_pixel >= width or y_pixel < 0 or y_pixel >= height:
            return jsonify({"error": f"Coordinates ({latitude}, {longitude}) fall outside the downloaded tile bounds"}), 400



        # Set the image for the SAM predictor
        predictor.set_image(image_rgb)

        # Perform segmentation with SAM
        input_point = np.array([[x_pixel, y_pixel]])
        input_label = np.array([1])  # 1 for foreground

        # Get masks from SAM
        masks, scores, _ = predictor.predict(
            point_coords=input_point,
            point_labels=input_label,
            multimask_output=True,
        )

        # Use the mask with the highest score
        best_mask_idx = np.argmax(scores)
        mask = masks[best_mask_idx]

        # Convert mask to binary image
        mask_uint8 = (mask * 255).astype(np.uint8)

        # Find contours in the binary mask
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Convert contours to list of points in both pixel and geographic coordinates
        contour_points_pixel = []
        contour_points_geo = []

        if contours:
            # Use the largest contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Simplify the contour
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

            # Extract points from the simplified contour
            for point in simplified_contour:
                px, py = point[0]
                contour_points_pixel.append([int(px), int(py)])

                # Convert pixel coordinates to geographic coordinates
                lon_geo = bbox['xmin'] + (px / width) * (bbox['xmax'] - bbox['xmin'])
                lat_geo = bbox['ymax'] - (py / height) * (bbox['ymax'] - bbox['ymin'])
                contour_points_geo.append([float(lon_geo), float(lat_geo)])

        # Create GeoJSON directly without saving files
        geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "field_boundary": True,
                        "source_coordinates": {
                            "latitude": latitude,
                            "longitude": longitude
                        },
                        "zoom_level": zoom_level,
                        "tile_source": tile_source,
                        "segmentation_score": float(scores[best_mask_idx])
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [contour_points_geo + [contour_points_geo[0]]] if contour_points_geo else []
                    }
                }
            ]
        }

        # Return clean GeoJSON format directly
        return jsonify(geojson)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
