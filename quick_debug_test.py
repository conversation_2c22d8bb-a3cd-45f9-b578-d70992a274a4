#!/usr/bin/env python3
"""
Quick debug test to see logs
"""

import requests
import json

def quick_test():
    """Quick test"""
    base_url = 'http://localhost:5000'
    
    print('🔍 Quick Debug Test')
    print('=' * 30)
    
    # Initialize
    print('1. Initialize...')
    response = requests.post(f'{base_url}/initialize', json={}, timeout=30)
    print(f'   Status: {response.status_code}')
    
    # Test coordinates
    print('2. Test coordinates...')
    test_data = {
        'latitude': 36.774843,
        'longitude': 9.723611,
        'zoom_level': 16
    }
    
    print('   Making request...')
    print('   🔍 Check server terminal for debug logs!')
    
    response = requests.post(
        f'{base_url}/segment-coordinates', 
        json=test_data, 
        timeout=60  # Short timeout to see what happens
    )
    
    print(f'   Status: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        features = data.get('features', [])
        if features:
            score = features[0]['properties']['segmentation_score']
            print(f'   Score: {score}')

if __name__ == '__main__':
    quick_test()
