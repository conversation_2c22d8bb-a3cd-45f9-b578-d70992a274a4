["test.py::TestFieldBoundaryAPI::test_health_check", "test.py::TestFieldBoundaryAPI::test_initialize_and_segment_tiff", "test.py::TestFieldBoundaryAPI::test_initialize_model_file_not_found", "test.py::TestFieldBoundaryAPI::test_initialize_model_success", "test.py::TestFieldBoundaryAPI::test_invalid_coordinates", "test.py::TestFieldBoundaryAPI::test_missing_parameters", "test.py::TestFieldBoundaryAPI::test_segment_coordinates", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_default_zoom_level", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_invalid_latitude", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_invalid_longitude", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_invalid_parameters", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_invalid_zoom_level", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_missing_latitude", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_missing_longitude", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_missing_parameters", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_model_not_initialized", "test.py::TestFieldBoundaryAPI::test_segment_coordinates_success", "test.py::TestFieldBoundaryAPI::test_segment_tiff_file_not_found", "test.py::TestFieldBoundaryAPI::test_segment_tiff_missing_parameters", "test.py::TestFieldBoundaryAPI::test_segment_tiff_model_not_initialized", "test.py::TestFieldBoundaryAPI::test_segment_tiff_real_file", "test.py::TestFieldBoundaryAPI::test_segment_tiff_success", "test.py::TestWorkflowFunctions::test_complete_workflow_structure", "test.py::TestWorkflowFunctions::test_download_satellite_image", "test.py::TestWorkflowFunctions::test_process_masks_to_field_boundaries", "test.py::TestWorkflowFunctions::test_save_image_to_bucket", "test.py::TestWorkflowFunctions::test_workflow_error_handling", "test_clean.py::TestFieldBoundaryAPI::test_health_check", "test_clean.py::TestFieldBoundaryAPI::test_initialize_model_file_not_found", "test_clean.py::TestFieldBoundaryAPI::test_segment_coordinates_invalid_parameters", "test_clean.py::TestFieldBoundaryAPI::test_segment_coordinates_missing_parameters", "test_clean.py::TestFieldBoundaryAPI::test_segment_coordinates_model_not_initialized", "test_clean.py::TestFieldBoundaryAPI::test_segment_coordinates_success", "test_clean.py::TestFieldBoundaryAPI::test_segment_tiff_file_not_found", "test_clean.py::TestFieldBoundaryAPI::test_segment_tiff_missing_parameters", "test_clean.py::TestFieldBoundaryAPI::test_segment_tiff_model_not_initialized", "test_clean.py::TestFieldBoundaryAPI::test_segment_tiff_success"]