# Field Boundary API

A Flask API for detecting field boundaries from satellite imagery using Google Vertex AI's Segment Anything Model (SAM). This API helps in agricultural field delineation and mapping.

## Overview

This API allows users to:
1. Initialize the SAM model
2. **NEW**: Automatically download satellite imagery for any geographic coordinates
3. Perform field boundary segmentation on the imagery using AI
4. Return the segmented boundary as GeoJSON with geographic coordinates
5. Visualize field boundaries with blue boundary overlays
6. Process both automatically downloaded satellite imagery and pre-existing GeoTIFF files

The API now features a fully automated workflow that eliminates the need for manual TIFF file uploads. Simply provide latitude, longitude, and zoom level, and the API handles everything from satellite image download to field boundary detection.

## Installation

### Local Installation

1. Clone this repository:
```bash
git clone https://gitlab.com/kalil.dimassi/field_boundary.git
cd field_boundary
```

2. Install the required dependencies:
```bash
pip install -r requirements.txt
```

3. Configure Google Cloud Vertex AI credentials:

- Create or use a GCP project with Vertex AI enabled
- Create a service account with Vertex AI permissions and download its JSON key (save it as keyfile.json in the project root)
- Create a .env file in the project root:

```env
GOOGLE_APPLICATION_CREDENTIALS=keyfile.json
GCP_PROJECT=your-project-id
GCP_LOCATION=us-central1
```

- Start the API and call /initialize (no body needed) to verify setup

### Key Dependencies

- Flask: Web framework for the API
- Flask: Web framework for the API
- Google Cloud Vertex AI: Managed SAM model inference (google-cloud-aiplatform)
- python-dotenv: Environment configuration
- Rasterio: For working with geospatial raster data (GeoTIFF)
- OpenCV: For image processing and overlays

For a complete list of dependencies, see `requirements.txt`.

## Usage

### Starting the API

Run the Flask application:
```bash
python main.py
```

The API will be available at `http://localhost:5000`.

### API Endpoints and Examples

#### Health Check
```
GET /health
```
Returns the health status of the API.

Example:
```bash
curl http://localhost:5000/health
```

Response:
```json
{
  "status": "healthy"
}
```

#### Initialize Model
```
POST /initialize
```
Initializes Google Vertex AI SAM using .env or request overrides.

Example:
```bash
curl -X POST http://localhost:5000/initialize \
  -H "Content-Type: application/json" \
  -d '{}'
```

Optional request fields:
- project: GCP project ID (defaults to GCP_PROJECT/.env)
- location: Vertex AI region (defaults to GCP_LOCATION/.env)
- keyfile: Path to service account JSON (defaults to GOOGLE_APPLICATION_CREDENTIALS/.env)

Example response:
```json
{
  "status": "Vertex AI initialized",
  "project": "your-project-id",
  "location": "us-central1"
}
```

#### Segment by Coordinates
```
POST /segment-coordinates
```
Automatically downloads satellite imagery for the specified coordinates and segments the field using Vertex AI SAM.

Example:
```bash
curl -X POST http://localhost:5000/segment-coordinates \
  -H "Content-Type: application/json" \
  -d '{"latitude": 37.000260, "longitude": 9.721093, "zoom_level": 16}'
```

Response (GeoJSON excerpt):
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "segmentation_score": 0.97,
        "outputs": {"input_png": "temp_images/input_...png", "mask_png": "...", "overlay_png": "..."}
      },
      "geometry": {"type": "Polygon", "coordinates": [[...]]}
    }
  ]
}
```

#### Segment TIFF
```
POST /segment-tiff
```
Segments a field boundary directly from a TIFF file using Vertex AI SAM.

Example:
```bash
curl -X POST http://localhost:5000/segment-tiff \
  -H "Content-Type: application/json" \
  -d '{"image_path": "NDVI_11-3.tiff", "x": 50, "y": 50}'
```

Response:
```json
{
  "status": "success",
  "image_dimensions": {"width": 512, "height": 512},
  "selected_point": {
    "pixel": {"x": 150, "y": 200},
    "geographic": {"longitude": 9.72, "latitude": 37.0}
  },
  "contour_points": {
    "pixel": [[[120, 180], [130, 190], ...]],
    "geographic": [[[9.71, 37.01], [9.72, 37.02], ...]]
  },
  "mask_path": "satellite_sam_mask.png",
  "visualization_path": "satellite_sam_segmentation.png",
  "blue_boundary_path": "satellite_sam_blue_boundary.png",
  "geojson_path": "satellite_sam_boundary.geojson",
  "score": 0.95
}
```

#### Segment TIFF
```
POST /segment-tiff
```
Segments a field boundary directly from a TIFF file using the SAM model. This is a simplified version of the `/process-tiff` endpoint that focuses specifically on segmentation with the SAM model.

Example:
```bash
curl -X POST http://localhost:5000/segment-tiff \
  -H "Content-Type: application/json" \
  -d '{"image_path": "NDVI_11-3.tiff", "x": 150, "y": 200}'
```

Response:
```json
{
  "status": "success",
  "image_dimensions": {"width": 512, "height": 512},
  "selected_point": {
    "pixel": {"x": 150, "y": 200},
    "geographic": {"longitude": 9.72, "latitude": 37.0}
  },
  "contour_points": {
    "pixel": [[[120, 180], [130, 190], ...]],
    "geographic": [[[9.71, 37.01], [9.72, 37.02], ...]]
  },
  "blue_boundary_path": "NDVI_11-3_sam_blue_boundary.png",
  "geojson_path": "NDVI_11-3_sam_boundary.geojson",
  "score": 0.95
}
```

#### Segment Coordinates (New!)
```
POST /segment-coordinates
```
Automatically downloads satellite imagery for given coordinates and performs field boundary segmentation. This endpoint eliminates the need for manual TIFF file uploads by handling the entire workflow from coordinates to field boundaries.

**Parameters:**
- `latitude` (required): Target latitude coordinate (float, -90 to 90)
- `longitude` (required): Target longitude coordinate (float, -180 to 180)
- `zoom_level` (optional): Zoom level for tile download (integer, 1-20, default: 16)
- `tile_source` (optional): Tile source for satellite imagery (string, default: "Satellite")

Example:
```bash
curl -X POST http://localhost:5000/segment-coordinates \
  -H "Content-Type: application/json" \
  -d '{"latitude": 37.000260, "longitude": 9.721093, "zoom_level": 16}'
```

Response:
```json
{
  "status": "success",
  "input_coordinates": {
    "latitude": 37.000260,
    "longitude": 9.721093,
    "zoom_level": 16,
    "tile_source": "Satellite"
  },
  "image_metadata": {
    "width": 512,
    "height": 512,
    "downloaded_image_path": "satellite_37.000260_9.721093_16.tif",
    "bounding_box": {
      "xmin": 9.711093,
      "xmax": 9.731093,
      "ymin": 36.990260,
      "ymax": 37.010260
    }
  },
  "selected_point": {
    "pixel": {"x": 256, "y": 256},
    "geographic": {"longitude": 9.721093, "latitude": 37.000260}
  },
  "contour_points": {
    "pixel": [[120, 180], [130, 190], ...],
    "geographic": [[9.71, 37.01], [9.72, 37.02], ...]
  },
  "blue_boundary_path": "boundary_37.000260_9.721093_16_blue_boundary.png",
  "geojson_path": "boundary_37.000260_9.721093_16.geojson",
  "score": 0.95
}
```

**Key Features of the New Endpoint:**
- **Automatic Image Download**: No need to manually upload TIFF files
- **Global Coverage**: Works with any latitude/longitude coordinates worldwide
- **Flexible Zoom Levels**: Support for zoom levels 1-20 for different detail levels
- **Real-time Processing**: Downloads imagery and performs segmentation in a single API call
- **Geographic Accuracy**: Returns both pixel and geographic coordinates for all boundary points
- **GeoJSON Output**: Standard format for easy integration with mapping applications
- **Error Handling**: Comprehensive validation and error reporting

**Use Cases:**
- Agricultural field mapping and boundary detection
- Land parcel identification for real estate
- Environmental monitoring and land use analysis
- Precision agriculture applications
- Automated surveying and mapping workflows
```

#### Get Image
```
GET /get-image/<filename>
```
Returns an image file (PNG) for visualization.

Example:
```bash
curl http://localhost:5000/get-image/satellite_sam_blue_boundary.png --output field_boundary.png
```

#### Get GeoJSON
```
GET /get-geojson/<filename>
```
Returns a GeoJSON file containing the field boundary.

Example:
```bash
curl http://localhost:5000/get-geojson/satellite_sam_boundary.geojson --output field_boundary.geojson
```

### Input and Output Details

#### Inputs

1. **For the `/initialize` endpoint:**
   - `project` (optional): GCP project ID. Defaults to `GCP_PROJECT` from .env
   - `location` (optional): Vertex AI region. Defaults to `GCP_LOCATION` from .env
   - `keyfile` (optional): Service account key path. Defaults to `GOOGLE_APPLICATION_CREDENTIALS` from .env

2. **For the `/segment-coordinates` endpoint:**
   - `latitude`: Latitude of the center point of the area of interest
   - `longitude`: Longitude of the center point of the area of interest
   - `zoom_level`: Zoom level for satellite imagery (typically 14-18, higher means more detail)


4. **For the `/segment-tiff` endpoint:**
   - `image_path`: Path to the GeoTIFF file to segment (e.g., "NDVI_11-3.tiff")
   - `x`: X-coordinate (pixel) of the point inside the field to segment
   - `y`: Y-coordinate (pixel) of the point inside the field to segment

#### Outputs

1. **From the `/segment-coordinates` endpoint:**
   - GeoJSON containing the field boundary polygon
   - Bounding box information of the downloaded satellite image


3. **From the `/segment-tiff` endpoint:**
   - Image dimensions
   - Selected point information (both pixel and geographic coordinates)
   - Contour points (both pixel and geographic coordinates)
   - Paths to generated visualization files:
     - Blue boundary visualization (original image with blue field boundary)
     - GeoJSON file with the field boundary
   - Segmentation score

## Visualization Features

The API provides several visualization options for field boundaries:

1. **Mask Visualization**: A binary mask showing the segmented field area
2. **Segmentation Visualization**: The original image with a green overlay showing the segmented field
3. **Blue Boundary Visualization**: The original image with field boundaries highlighted in blue

All visualizations can be accessed through the `/get-image/<filename>` endpoint, where the filename is returned in the response from the `/segment-tiff` or `/segment-coordinates` endpoints.

## Google Colab Support

The API can be run in Google Colab using the provided Jupyter notebook:

1. Open `Field_boundary.ipynb` in Google Colab
2. Run the cells to install dependencies and download the SAM model
3. Use the interactive map to select field locations for segmentation

## Project Structure

- `main.py`: Flask API endpoints
- `utils.py`: Helper functions for image processing and segmentation
- `test.py`: Unit tests for the API
- `requirements.txt`: Required Python packages
- `README.md`: Project documentation
- `Field_boundary.ipynb`: Jupyter notebook for Google Colab integration

## Testing

Run the tests:
```bash
python test.py
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Segment Anything Model (SAM)](https://github.com/facebookresearch/segment-anything)
- [Segment Geospatial](https://github.com/opengeos/segment-geospatial)
- [Leafmap](https://github.com/opengeos/leafmap)
