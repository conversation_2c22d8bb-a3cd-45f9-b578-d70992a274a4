import unittest
import json
import requests
import time
import os

class TestFieldBoundaryAPI(unittest.TestCase):

    # 🌍 CHANGE THESE COORDINATES TO TEST DIFFERENT LOCATIONS
    TEST_LATITUDE = 36.774843    # Tunisia agricultural field
    TEST_LONGITUDE = 9.723611    # Tunisia agricultural field
    TEST_ZOOM_LEVEL = 16         # Good zoom level for fields

    BASE_URL = "http://localhost:5000"

    def setUp(self):
        """Initialize the Vertex AI model before running tests"""
        print(f"\n🧪 Testing coordinates: {self.TEST_LATITUDE}, {self.TEST_LONGITUDE} (zoom: {self.TEST_ZOOM_LEVEL})")

        # Initialize the Vertex AI SAM model
        try:
            response = requests.post(f"{self.BASE_URL}/initialize", json={})
            if response.status_code != 200:
                print(f"⚠️ Vertex AI model initialization failed: {response.text}")
            else:
                print("✅ Vertex AI SAM model initialized successfully")
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to API server. Make sure Flask app is running!")
            raise


    def test_segment_coordinates(self):
        """Test field boundary detection with your coordinates"""
        print(f"🌍 Testing field segmentation...")

        start_time = time.time()
        response = requests.post(f"{self.BASE_URL}/segment-coordinates",
                               json={
                                   "latitude": self.TEST_LATITUDE,
                                   "longitude": self.TEST_LONGITUDE,
                                   "zoom_level": self.TEST_ZOOM_LEVEL
                               })
        end_time = time.time()

        # Check if request was successful
        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify GeoJSON response structure
        self.assertEqual(data['type'], 'FeatureCollection')
        self.assertIn('features', data)
        self.assertGreater(len(data['features']), 0)

        feature = data['features'][0]
        self.assertEqual(feature['type'], 'Feature')
        self.assertIn('geometry', feature)
        self.assertIn('properties', feature)
        self.assertEqual(feature['geometry']['type'], 'Polygon')
        self.assertIn('coordinates', feature['geometry'])

        # Print results
        print(f"✅ Segmentation successful! (took {end_time - start_time:.1f}s)")
        print(f"📊 Score: {feature['properties']['segmentation_score']:.3f}")
        print(f"🌍 GeoJSON Type: {data['type']}")
        print(f"📐 Features: {len(data['features'])}")

        # Show boundary coordinates
        coordinates = feature['geometry']['coordinates'][0]
        print(f"📐 Found {len(coordinates)} boundary points")

        # Print the complete GeoJSON response
        print("\n🗺️  COMPLETE GEOJSON RESPONSE:")
        print("=" * 50)
        import json
        print(json.dumps(data, indent=2))
        print("=" * 50)

    def test_segment_tiff_with_vertex_ai(self):
        """Test TIFF segmentation with Vertex AI SAM"""
        print(f"🖼️ Testing TIFF segmentation with Vertex AI...")

        # Test coordinates for NDVI_11-3.tiff
        test_x = 50
        test_y = 50
        tiff_file = "NDVI_11-3.tiff"

        # Check if test file exists
        if not os.path.exists(tiff_file):
            print(f"⚠️ Test file {tiff_file} not found, skipping test")
            self.skipTest(f"Test file {tiff_file} not found")
            return

        start_time = time.time()
        response = requests.post(f"{self.BASE_URL}/segment-tiff",
                               json={
                                   "image_path": tiff_file,
                                   "x": test_x,
                                   "y": test_y
                               })
        end_time = time.time()

        # Check if request was successful
        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify GeoJSON response structure
        self.assertEqual(data['type'], 'FeatureCollection')
        self.assertIn('features', data)
        self.assertGreater(len(data['features']), 0)

        feature = data['features'][0]
        self.assertEqual(feature['type'], 'Feature')
        self.assertIn('geometry', feature)
        self.assertIn('properties', feature)
        self.assertEqual(feature['geometry']['type'], 'Polygon')
        self.assertIn('coordinates', feature['geometry'])

        # Print results
        print(f"✅ TIFF segmentation successful! (took {end_time - start_time:.1f}s)")
        print(f"📊 Score: {feature['properties']['segmentation_score']:.3f}")
        print(f"🌍 GeoJSON Type: {data['type']}")
        print(f"📐 Features: {len(data['features'])}")

        # Show boundary coordinates
        coordinates = feature['geometry']['coordinates'][0]
        print(f"📐 Found {len(coordinates)} boundary points")


if __name__ == '__main__':
    print("🌍 Field Boundary Detection API Tests")
    print("=" * 50)
    print("📍 To test different coordinates, edit the TEST_LATITUDE and TEST_LONGITUDE values at the top of this file")
    print("🚀 Make sure the Flask server is running: python main.py")
    print()

    try:
        unittest.main(verbosity=2)
    except requests.exceptions.ConnectionError:
        print("\n❌ Cannot connect to API server!")
        print("   Make sure Flask app is running: python main.py")
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")

if __name__ == '__main__':
    unittest.main()
