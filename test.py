import unittest
import json
import requests
import time
import os

class TestFieldBoundaryAPI(unittest.TestCase):

    # 🌍 CHANGE THESE COORDINATES TO TEST DIFFERENT LOCATIONS
    TEST_LATITUDE = 36.774843    # Tunisia agricultural field
    TEST_LONGITUDE = 9.723611    # Tunisia agricultural field
    TEST_ZOOM_LEVEL = 16         # Good zoom level for fields

    BASE_URL = "http://localhost:5000"

    def setUp(self):
        """Initialize the model before running tests"""
        print(f"\n🧪 Testing coordinates: {self.TEST_LATITUDE}, {self.TEST_LONGITUDE} (zoom: {self.TEST_ZOOM_LEVEL})")

        # Initialize Vertex AI (uses .env by default)
        self.vertex_ready = False
        try:
            response = requests.post(f"{self.BASE_URL}/initialize", json={})
            if response.status_code == 200:
                self.vertex_ready = True
            else:
                print(f"⚠️ Vertex AI initialization failed: {response.text}")
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to API server. Make sure Flask app is running!")
            raise

    def test_health_check(self):
        resp = requests.get(f"{self.BASE_URL}/health")
        self.assertEqual(resp.status_code, 200)
        data = resp.json()
        self.assertEqual(data.get("status"), "healthy")

    def test_segment_tiff_success(self):
        if not self.vertex_ready:
            self.skipTest("Vertex AI SDK not available; skipping segmentation test")
        # User preference: NDVI_11-3.tiff at x=50, y=50
        image_path = "NDVI_11-3.tiff"
        alt_path = "__pycache__/NDVI_11-3.tiff"
        if not os.path.exists(image_path) and os.path.exists(alt_path):
            image_path = alt_path

        self.assertTrue(os.path.exists(image_path), f"Test TIFF not found at {image_path}")

        payload = {"image_path": image_path, "x": 50, "y": 50}
        resp = requests.post(f"{self.BASE_URL}/segment-tiff", json=payload)
        self.assertEqual(resp.status_code, 200, msg=resp.text)
        data = resp.json()
        self.assertEqual(data['type'], 'FeatureCollection')
        self.assertGreaterEqual(len(data['features']), 1)
        feature = data['features'][0]
        self.assertEqual(feature['geometry']['type'], 'Polygon')
        # Ensure outputs are present
        outputs = feature['properties'].get('outputs', {})
        self.assertIn('input_png', outputs)
        self.assertIn('mask_png', outputs)
        # Overlay may be None if no contour found, but keys exist
        self.assertIn('overlay_png', outputs)


    def test_segment_coordinates(self):
        """Test field boundary detection with your coordinates"""
        if not self.vertex_ready:
            self.skipTest("Vertex AI SDK not available; skipping segmentation test")
        print(f"🌍 Testing field segmentation...")

        start_time = time.time()
        response = requests.post(f"{self.BASE_URL}/segment-coordinates",
                               json={
                                   "latitude": self.TEST_LATITUDE,
                                   "longitude": self.TEST_LONGITUDE,
                                   "zoom_level": self.TEST_ZOOM_LEVEL
                               })
        end_time = time.time()

        # Check if request was successful
        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify GeoJSON response structure
        self.assertEqual(data['type'], 'FeatureCollection')
        self.assertIn('features', data)
        self.assertGreater(len(data['features']), 0)

        feature = data['features'][0]
        self.assertEqual(feature['type'], 'Feature')
        self.assertIn('geometry', feature)
        self.assertIn('properties', feature)
        self.assertEqual(feature['geometry']['type'], 'Polygon')
        self.assertIn('coordinates', feature['geometry'])

        # Print results
        print(f"✅ Segmentation successful! (took {end_time - start_time:.1f}s)")
        print(f"📊 Score: {feature['properties']['segmentation_score']:.3f}")
        print(f"🌍 GeoJSON Type: {data['type']}")
        print(f"📐 Features: {len(data['features'])}")

        # Show boundary coordinates
        coordinates = feature['geometry']['coordinates'][0]
        print(f"📐 Found {len(coordinates)} boundary points")

        # Print the complete GeoJSON response
        print("\n🗺️  COMPLETE GEOJSON RESPONSE:")
        print("=" * 50)
        import json
        print(json.dumps(data, indent=2))
        print("=" * 50)




if __name__ == '__main__':
    print("🌍 Field Boundary Detection API Tests")
    print("=" * 50)
    print("📍 To test different coordinates, edit the TEST_LATITUDE and TEST_LONGITUDE values at the top of this file")
    print("🚀 Make sure the Flask server is running: python main.py")
    print()

    try:
        unittest.main(verbosity=2)
    except requests.exceptions.ConnectionError:
        print("\n❌ Cannot connect to API server!")
        print("   Make sure Flask app is running: python main.py")
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")

if __name__ == '__main__':
    unittest.main()
