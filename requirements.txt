# Core API dependencies
flask==3.0.3
numpy==1.26.4
opencv-python==*********
Pillow==10.4.0
requests==2.32.3

# Testing dependencies
pytest==8.3.5
pytest-flask==1.2.0

# Google Cloud Vertex AI dependencies
google-cloud-aiplatform==1.38.1
google-auth==2.23.4
google-cloud-storage==2.10.0
python-dotenv==1.0.0

# Optional: CORS support for web applications
flask-cors==5.0.0

# Note: Migrated from local SAM model to Google Vertex AI
# Removed dependencies: torch, torchvision, segment-anything, rasterio, pyproj,
# scikit-image, matplotlib, shapely, geopandas, json5
# The new implementation uses Google Cloud Vertex AI for cloud-based image segmentation
