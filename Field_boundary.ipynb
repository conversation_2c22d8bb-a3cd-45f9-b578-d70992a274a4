%pip install segment-geospatial


using_colab = True

if using_colab:
    import torch
    import torchvision
    print("PyTorch version:", torch.__version__)
    print("Torchvision version:", torchvision.__version__)
    print("CUDA is available:", torch.cuda.is_available())
    import sys
    !{sys.executable} -m pip install opencv-python matplotlib
    !{sys.executable} -m pip install 'git+https://github.com/facebookresearch/segment-anything.git'


    !wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

import numpy as np
import torch
import matplotlib.pyplot as plt
import cv2

def show_mask(mask, ax, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)

def show_points(coords, labels, ax, marker_size=375):
    pos_points = coords[labels==1]
    neg_points = coords[labels==0]
    ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)
    ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)

def show_box(box, ax):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))

import sys
sys.path.append("..")
from segment_anything import sam_model_registry, SamPredictor

sam_checkpoint = "sam_vit_h_4b8939.pth"
model_type = "vit_h"

device = "cuda"

sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
sam.to(device=device)

predictor = SamPredictor(sam)

from ipyleaflet import Map, Polygon, basemaps
import rasterio
from pyproj import Proj, transform
import json
import cv2
import numpy as np
import leafmap
from PIL import Image
from samgeo import SamGeo, tms_to_geotiff
from skimage.measure import approximate_polygon




# Create a Leafmap
m = leafmap.Map(center=[37.000260, 9.721093], zoom=16, height="800px")
m.add_basemap("SATELLITE")

# Global list to store the coordinates of clicked points
clicked_points = []
drawn_polygons = []

# Function to convert latitude and longitude to pixel coordinates
def latlon_to_pixel(lat, lon, image_width, image_height, min_lat, max_lat, min_lon, max_lon):
    x = int(((lon - min_lon) / (max_lon - min_lon)) * image_width)
    y = int(((max_lat - lat) / (max_lat - min_lat)) * image_height)
    return x, y

# Function to perform segmentation
def perform_segmentation(lat, lon, ymin, ymax, xmin, xmax):
    # Load the GeoTIFF image
    image_path = 'satellite.tif'
    with rasterio.open(image_path) as src:
        # Read the first band
        image = src.read(1)
        # Get the image dimensions
        image_width = src.width
        image_height = src.height
        # Get the geographic bounds
        bounds = src.bounds
        min_lon = xmin
        max_lon = xmax
        min_lat = ymin
        max_lat = ymax
        # Get the transformation parameters
        transform_params = src.transform

    # Convert latitude and longitude to pixel coordinates
    x, y = latlon_to_pixel(lat, lon, image_width, image_height, min_lat, max_lat, min_lon, max_lon)

    # Assuming predictor is defined elsewhere in your code
    point_coords = np.array([[x, y]])  # Reshape to (1, 2)
    point_labels = np.array([1])  # Assuming label is 1
    masks, scores, logits = predictor.predict(
        point_coords=point_coords,
        point_labels=point_labels,
        multimask_output=True,
    )
    print("x_pixel=", x)
    print("y_pixel", y)

    # Minimum length for a contour segment to be considered valid
    MIN_CONTOUR_LENGTH = 10
    # Display the segmentation result
    i = 0

    # Convert mask to suitable datatype
    mask_uint8 = (masks[i] * 255).astype(np.uint8)

   # Find the contours of the segmented region
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    contour_points_list = []
    for contour in contours:
        # Extract coordinates of contour points
        contour_points = contour.squeeze(axis=1)
        # Check if the contour has a certain minimum length
        if len(contour_points) > MIN_CONTOUR_LENGTH:
            # Simplify the contour points using Douglas-Peucker algorithm
            simplified_contour = approximate_polygon(contour_points, tolerance=2.0)
            # Convert each contour point to geographic coordinates and append to the list
            contour_coords = []
            for contour_point in simplified_contour:
                x, y = contour_point[0], contour_point[1]

                # Convert pixel coordinates to geographic coordinates
                lon, lat = transform_params * (x, y)

                # Convert to EPSG:4326
                lon_4326, lat_4326 = geographic_to_epsg4326(lon, lat)

                # Append contour point to the list
                contour_coords.append([lat_4326, lon_4326])

            # Append contour coordinates to the list
            contour_points_list.append(contour_coords)

    # Save contour points to a file
    save_contour_points(contour_points_list)

    # Draw the polygon on the map
    polygon = Polygon(
        locations=contour_points_list,
        color='blue',
        fill_color='transparent',
        fill_opacity=0.4
    )
    m.add_layer(polygon)
     # Add the drawn polygon to the list
    drawn_polygons.append(polygon)


def geographic_to_epsg4326(lon1, lat1):
    source_crs = Proj(init='epsg:3857')
    target_crs = Proj(init='epsg:4326')
    lon_4326, lat_4326 = transform(source_crs, target_crs, lon1, lat1)
    return lon_4326, lat_4326

def save_contour_points(contour_points):
    polygon = {
        "type": "Polygon",
        "coordinates": [contour_points]  # Assuming contour_points is a list of coordinates
    }
    with open('contour_points.json', 'w') as file:
        json.dump(polygon, file)

# Function to display coordinates when clicking on the map
def handle_click(**kwargs):
    global clicked_points
    if kwargs.get('type') == 'click':
        lat, lon = kwargs.get('coordinates')
        zoom_level = int(m.zoom)
        print(f"Latitude: {lat}, Longitude: {lon}, Zoom Level: {zoom_level}")
        # Calculate parameters for the polygon
        center_lat = lat
        center_lon = lon
        width = 0.02  # Adjust as needed
        height = 0.02  # Adjust as needed
        # Calculate the xmin, xmax, ymin, and ymax
        xmin = center_lon - (width / 2)
        xmax = center_lon + (width / 2)
        ymin = center_lat - (height / 2)
        ymax = center_lat + (height / 2)
        print(f"xmin: {xmin}, xmax: {xmax}, ymin: {ymin}, ymax: {ymax}")
        # Define the vertices of the polygon
        contour_points = [
            [ymin, xmin],  # Lower left
            [ymin, xmax],  # Lower right
            [ymax, xmax],  # Upper right
            [ymax, xmin],  # Upper left
            [ymin, xmin]   # Lower left (closing point)
        ]

        # Draw the polygon with the contour point list
        polygon = Polygon(
            locations=contour_points,
            color='transparent',
            fill_color='transparent',
            fill_opacity=0,
            opacity=0,
            weight=0,
            transform=True
        )

        # Export the image
        bbox = [xmin, ymin, xmax, ymax]
        image = "satellite.tif"
        tms_to_geotiff(output=image, bbox=bbox, zoom=zoom_level, source="Satellite", overwrite=True)
        print("Image exported successfully!")
        image = cv2.imread('satellite.tif')
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        predictor.set_image(image)

        # Call the segmentation function
        perform_segmentation(center_lat, center_lon,ymin,ymax,xmin,xmax)

# Add click event listener to the map
m.on_interaction(handle_click)

# Display the map
m
