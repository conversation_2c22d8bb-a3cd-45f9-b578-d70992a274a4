{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"703d411fea8d4263b48e7b9fb30d33d2": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "OpenStreetMap.Mapnik", "description_tooltip": null, "disabled": false, "indent": false, "layout": "IPY_MODEL_85e76755514d49798f43acc10b639ddf", "style": "IPY_MODEL_6c042ec960a041b38bb1f37dea650385", "value": true}}, "0de9971b5ea3477c841871a111617c95": {"model_module": "jupyter-leaflet", "model_name": "LeafletTileLayerModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletTileLayerModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletTileLayerView", "attribution": "&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors", "base": true, "bottom": true, "bounds": null, "detect_retina": false, "loading": true, "max_native_zoom": null, "max_zoom": 19, "min_native_zoom": null, "min_zoom": 1, "name": "OpenStreetMap", "no_wrap": false, "opacity": 1, "options": ["attribution", "bounds", "detect_retina", "max_native_zoom", "max_zoom", "min_native_zoom", "min_zoom", "no_wrap", "tile_size", "tms", "zoom_offset"], "pane": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "show_loading": false, "subitems": [], "tile_size": 256, "tms": false, "url": "https://tile.openstreetmap.org/{z}/{x}/{y}.png", "visible": true, "zoom_offset": 0}}, "cf70a100712346e8b01ccae2463476c9": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "OpenStreetMap.Mapnik", "description_tooltip": null, "disabled": false, "indent": false, "layout": "IPY_MODEL_ce0492a0a69147acbde3037f37e648cc", "style": "IPY_MODEL_20311a61e17842e582e6f06b27df88bb", "value": true}}, "9922b327e0a9464aaf37bb3e5ad901e3": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "OpenStreetMap.Mapnik", "description_tooltip": null, "disabled": false, "indent": false, "layout": "IPY_MODEL_ddbc7064a1014f45bf27fb9a722cff30", "style": "IPY_MODEL_cfacd7b795df404c9622c72000ba18b9", "value": true}}, "eba2cd297a1a4ba9b4d0a9878bc5ccb9": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "OpenStreetMap", "description_tooltip": null, "disabled": false, "indent": false, "layout": "IPY_MODEL_db48658cb03d407487d57c995a364324", "style": "IPY_MODEL_8f7245fda210467685a10d87e65e48ad", "value": true}}, "aa6874af9b1144f8a31a6d92c9db0d6f": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "OpenStreetMap", "description_tooltip": null, "disabled": false, "indent": false, "layout": "IPY_MODEL_f2696dc961db46a2ba3d8cc3fb966f02", "style": "IPY_MODEL_cc2e3e5a72524e34ada89d26864b7ff5", "value": true}}, "85e76755514d49798f43acc10b639ddf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "18px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "25ex"}}, "6c042ec960a041b38bb1f37dea650385": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ce0492a0a69147acbde3037f37e648cc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "18px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "25ex"}}, "20311a61e17842e582e6f06b27df88bb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ddbc7064a1014f45bf27fb9a722cff30": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "18px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "25ex"}}, "cfacd7b795df404c9622c72000ba18b9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "db48658cb03d407487d57c995a364324": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "18px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "25ex"}}, "8f7245fda210467685a10d87e65e48ad": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f2696dc961db46a2ba3d8cc3fb966f02": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "18px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "25ex"}}, "cc2e3e5a72524e34ada89d26864b7ff5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "be87846cfa154bf19d9993d8e99f340f": {"model_module": "@jupyter-widgets/controls", "model_name": "CheckboxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "Google Satellite", "description_tooltip": null, "disabled": false, "indent": false, "layout": "IPY_MODEL_3f3304c7c3914153ba0e00fd155df197", "style": "IPY_MODEL_9ca674c0fd37485c91d5466b57e5f047", "value": true}}, "6250781fb8a24c7d89fb8a160b1457a5": {"model_module": "jupyter-leaflet", "model_name": "LeafletTileLayerModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletTileLayerModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletTileLayerView", "attribution": "Google", "base": false, "bottom": true, "bounds": null, "detect_retina": false, "loading": false, "max_native_zoom": null, "max_zoom": 24, "min_native_zoom": null, "min_zoom": 0, "name": "Google Satellite", "no_wrap": false, "opacity": 1, "options": ["attribution", "bounds", "detect_retina", "max_native_zoom", "max_zoom", "min_native_zoom", "min_zoom", "no_wrap", "tile_size", "tms", "zoom_offset"], "pane": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "show_loading": false, "subitems": [], "tile_size": 256, "tms": false, "url": "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}&key=YOUR-API-KEY", "visible": true, "zoom_offset": 0}}, "3f3304c7c3914153ba0e00fd155df197": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "18px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "25ex"}}, "9ca674c0fd37485c91d5466b57e5f047": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a4f310d830324a2eb8c2ab3c8b71b3fd": {"model_module": "jupyter-leaflet", "model_name": "LeafletMapModel", "model_module_version": "^0.19", "state": {"_dom_classes": [], "_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletMapModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletMapView", "bottom": 3265739, "bounce_at_zoom_limits": true, "box_zoom": true, "center": [36.9917560470312, 9.747340679168703], "close_popup_on_click": true, "controls": ["IPY_MODEL_6c053b04f1604d4a9827ab72d0aab9e1", "IPY_MODEL_460a23e0402f4b30bdbb0095b7980a43", "IPY_MODEL_08f79026859c4970a85eb7297c735447", "IPY_MODEL_791febb6e3314bad9a03bb6e1cd332c7", "IPY_MODEL_2899e151dfd545c2bc79af3f25257d3d", "IPY_MODEL_7ad6c3ab662b4f9991bfbd25df96b727"], "crs": {"name": "EPSG3857", "custom": false}, "default_style": "IPY_MODEL_9c914e28a711442b990b1b2d02fb4b19", "double_click_zoom": true, "dragging": true, "dragging_style": "IPY_MODEL_1d036b6c051f40ba959b8ee997b12e3e", "east": 9.785428047180178, "fullscreen": false, "inertia": true, "inertia_deceleration": 3000, "inertia_max_speed": 1500, "interpolation": "bilinear", "keyboard": true, "keyboard_pan_offset": 80, "keyboard_zoom_offset": 1, "layers": ["IPY_MODEL_0de9971b5ea3477c841871a111617c95", "IPY_MODEL_6250781fb8a24c7d89fb8a160b1457a5", "IPY_MODEL_5b15978ecdb4443f8e782088620c1e8f"], "layout": "IPY_MODEL_690f5108c42545a0904155a3d38af197", "left": 4420546, "max_zoom": 24, "min_zoom": null, "modisdate": "2024-10-23", "north": 37.00546578455302, "options": ["bounce_at_zoom_limits", "box_zoom", "center", "close_popup_on_click", "double_click_zoom", "dragging", "fullscreen", "inertia", "inertia_deceleration", "inertia_max_speed", "interpolation", "keyboard", "keyboard_pan_offset", "keyboard_zoom_offset", "max_zoom", "min_zoom", "prefer_canvas", "scroll_wheel_zoom", "tap", "tap_tolerance", "touch_zoom", "world_copy_jump", "zoom", "zoom_animation_threshold", "zoom_delta", "zoom_snap"], "panes": {}, "prefer_canvas": false, "right": 4422321, "scroll_wheel_zoom": true, "south": 36.97804383779478, "style": "IPY_MODEL_9c914e28a711442b990b1b2d02fb4b19", "tap": true, "tap_tolerance": 15, "top": 3264939, "touch_zoom": true, "west": 9.709253311157228, "window_url": "https://oe9fad9sf7-496ff2e9c6d22116-0-colab.googleusercontent.com/outputframe.html?vrz=colab_20241022-060119_RC00_688494744", "world_copy_jump": false, "zoom": 15, "zoom_animation_threshold": 4, "zoom_delta": 1, "zoom_snap": 1}}, "6c053b04f1604d4a9827ab72d0aab9e1": {"model_module": "jupyter-leaflet", "model_name": "LeafletZoomControlModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletZoomControlModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletZoomControlView", "options": ["position", "zoom_in_text", "zoom_in_title", "zoom_out_text", "zoom_out_title"], "position": "topleft", "zoom_in_text": "+", "zoom_in_title": "Zoom in", "zoom_out_text": "-", "zoom_out_title": "Zoom out"}}, "460a23e0402f4b30bdbb0095b7980a43": {"model_module": "jupyter-leaflet", "model_name": "LeafletAttributionControlModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletAttributionControlModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletAttributionControlView", "options": ["position", "prefix"], "position": "bottomright", "prefix": "ipyleaflet"}}, "08f79026859c4970a85eb7297c735447": {"model_module": "jupyter-leaflet", "model_name": "LeafletFullScreenControlModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletFullScreenControlModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletFullScreenControlView", "options": ["position"], "position": "topleft"}}, "791febb6e3314bad9a03bb6e1cd332c7": {"model_module": "jupyter-leaflet", "model_name": "LeafletDrawControlModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletDrawControlModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletDrawControlView", "circle": {"shapeOptions": {"color": "#3388ff"}, "repeatMode": false}, "circlemarker": {}, "data": [], "edit": true, "marker": {"shapeOptions": {"color": "#3388ff"}, "repeatMode": false}, "options": ["position"], "polygon": {"repeatMode": false}, "polyline": {"repeatMode": false}, "position": "topleft", "rectangle": {"shapeOptions": {"color": "#3388ff"}, "repeatMode": false}, "remove": true}}, "2899e151dfd545c2bc79af3f25257d3d": {"model_module": "jupyter-leaflet", "model_name": "LeafletScaleControlModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletScaleControlModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletScaleControlView", "imperial": true, "max_width": 100, "metric": true, "options": ["imperial", "max_width", "metric", "position", "update_when_idle"], "position": "bottomleft", "update_when_idle": false}}, "7ad6c3ab662b4f9991bfbd25df96b727": {"model_module": "jupyter-leaflet", "model_name": "LeafletWidgetControlModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletWidgetControlModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletWidgetControlView", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "options": ["position", "transparent_bg"], "position": "topright", "transparent_bg": false, "widget": "IPY_MODEL_76e06e76657047379b57099d10b81638"}}, "9c914e28a711442b990b1b2d02fb4b19": {"model_module": "jupyter-leaflet", "model_name": "LeafletMapStyleModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletMapStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "cursor": "grab"}}, "1d036b6c051f40ba959b8ee997b12e3e": {"model_module": "jupyter-leaflet", "model_name": "LeafletMapStyleModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletMapStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "cursor": "move"}}, "690f5108c42545a0904155a3d38af197": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "800px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bb093a9a25c043828796d74b08f2e780": {"model_module": "jupyter-leaflet", "model_name": "LeafletMapStyleModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletMapStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "cursor": "grab"}}, "76e06e76657047379b57099d10b81638": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_fbf4b8f573024c24a150f3a4609c1dba"], "layout": "IPY_MODEL_96a24efb32d84fdfb8ef6deb0b9a60a2"}}, "fbf4b8f573024c24a150f3a4609c1dba": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonView", "button_style": "", "description": "", "description_tooltip": null, "disabled": false, "icon": "wrench", "layout": "IPY_MODEL_ec2a374a6dd6429593928a629b97fef5", "style": "IPY_MODEL_6751dec0923c4a268252d49f10ffcf2b", "tooltip": "<PERSON><PERSON><PERSON>", "value": false}}, "96a24efb32d84fdfb8ef6deb0b9a60a2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ec2a374a6dd6429593928a629b97fef5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "28px", "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": "0px 0px 0px 4px", "right": null, "top": null, "visibility": null, "width": "28px"}}, "6751dec0923c4a268252d49f10ffcf2b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e7d5725d8da846508c532ca6d397f37a": {"model_module": "jupyter-leaflet", "model_name": "LeafletPolygonModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletPolygonModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletPolygonView", "base": false, "bottom": false, "color": "blue", "dash_array": null, "draggable": false, "fill": true, "fill_color": "transparent", "fill_opacity": 0.4, "line_cap": "round", "line_join": "round", "locations": [[[37.00258587661471, 9.719982137884708], [36.99991467381162, 9.7167203353096], [36.998544789816705, 9.719638790245222], [36.998065324587834, 9.721441365352518], [36.99895575760658, 9.721441365352518], [36.99970919278574, 9.72229973445123], [36.99964069898705, 9.722900592820329], [37.000325634197445, 9.722900592820329], [37.000462620499114, 9.723243940459813], [37.00169548610792, 9.72152720226239], [37.001490009894745, 9.720926343893291], [37.001969453528346, 9.720582996253807], [37.0020379452292, 9.721098017713034], [37.00251738540748, 9.720067974794578]]], "name": "", "no_clip": true, "opacity": 1, "options": ["color", "dash_array", "draggable", "fill", "fill_color", "fill_opacity", "line_cap", "line_join", "no_clip", "opacity", "pointer_events", "smooth_factor", "stroke", "transform", "weight"], "pane": "", "pointer_events": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "rotation": true, "scaling": true, "smooth_factor": 1, "stroke": true, "subitems": [], "transform": false, "uniform_scaling": false, "weight": 5}}, "b206856f65da483db48d96047156928a": {"model_module": "jupyter-leaflet", "model_name": "LeafletPolygonModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletPolygonModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletPolygonView", "base": false, "bottom": false, "color": "blue", "dash_array": null, "draggable": false, "fill": true, "fill_color": "transparent", "fill_opacity": 0.4, "line_cap": "round", "line_join": "round", "locations": [[[37.01036300093934, 9.70865204217608], [37.01015753629785, 9.70856620526621], [37.0095411390408, 9.709209982090243], [37.00899322617132, 9.71002543273402], [37.008582288927244, 9.710411698828441], [37.008171349461506, 9.711141312562347], [37.007109745558765, 9.712343029300545], [37.00327414984719, 9.713501827583807], [37.00108229398111, 9.713973930588098], [36.999952093608954, 9.71659195633917], [37.00139052753053, 9.71835161299153], [37.00317140801507, 9.716248608699685], [37.00437005408371, 9.717621999257624], [37.00471252091782, 9.717836591532302], [37.0072124820683, 9.714875218141744], [37.00745220005057, 9.714617707412131], [37.00769191727686, 9.714574788957195], [37.00933567217759, 9.716634874794106], [37.0095411390408, 9.716634874794106], [37.00964387226411, 9.716334445609556], [37.01002055956161, 9.710240025008696], [37.01029451278723, 9.70916706363531], [37.01015753629785, 9.708866634450757], [37.01036300093934, 9.708780797540888]]], "name": "", "no_clip": true, "opacity": 1, "options": ["color", "dash_array", "draggable", "fill", "fill_color", "fill_opacity", "line_cap", "line_join", "no_clip", "opacity", "pointer_events", "smooth_factor", "stroke", "transform", "weight"], "pane": "", "pointer_events": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "rotation": true, "scaling": true, "smooth_factor": 1, "stroke": true, "subitems": [], "transform": false, "uniform_scaling": false, "weight": 5}}, "ae8b17844b55407cb59c39a6d042d123": {"model_module": "jupyter-leaflet", "model_name": "LeafletPolygonModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletPolygonModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletPolygonView", "base": false, "bottom": false, "color": "blue", "dash_array": null, "draggable": false, "fill": true, "fill_color": "transparent", "fill_opacity": 0.4, "line_cap": "round", "line_join": "round", "locations": [[[37.01303858807343, 9.718994867218411], [37.01293585124053, 9.718823193398668], [37.012422164992856, 9.718994867218411], [37.01170299841306, 9.718737356488797], [37.01122355024585, 9.718737356488797], [37.00951121067326, 9.720110747046737], [37.00927148005461, 9.723286712711973], [37.00930572733214, 9.724016326445879], [37.011292043026344, 9.726720189106823], [37.01153176727197, 9.726333923012403], [37.01166875221569, 9.725647227733432], [37.01207970556548, 9.724617184814978], [37.012901605598714, 9.723930489536007], [37.01286735994146, 9.720711605415836], [37.01303858807343, 9.719037785673347]]], "name": "", "no_clip": true, "opacity": 1, "options": ["color", "dash_array", "draggable", "fill", "fill_color", "fill_opacity", "line_cap", "line_join", "no_clip", "opacity", "pointer_events", "smooth_factor", "stroke", "transform", "weight"], "pane": "", "pointer_events": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "rotation": true, "scaling": true, "smooth_factor": 1, "stroke": true, "subitems": [], "transform": false, "uniform_scaling": false, "weight": 5}}, "34182905292a46cf9ebacf6c58953eb4": {"model_module": "jupyter-leaflet", "model_name": "LeafletPolygonModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletPolygonModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletPolygonView", "base": false, "bottom": false, "color": "blue", "dash_array": null, "draggable": false, "fill": true, "fill_color": "transparent", "fill_opacity": 0.4, "line_cap": "round", "line_join": "round", "locations": [[[36.99843007086803, 9.719252527261498], [36.99671497713444, 9.718093728978237], [36.99448529744908, 9.716934930694975], [36.985051313894374, 9.71345853584519], [36.98391915723905, 9.71384480193961], [36.982478206219376, 9.71560445859197], [36.981517557044484, 9.716033643141326], [36.983095759998186, 9.717192441424588], [36.99263289845706, 9.723243943570512], [36.99695509258591, 9.723115188205705], [36.997881245082084, 9.721140939278666], [36.99843007086803, 9.71942420108124]]], "name": "", "no_clip": true, "opacity": 1, "options": ["color", "dash_array", "draggable", "fill", "fill_color", "fill_opacity", "line_cap", "line_join", "no_clip", "opacity", "pointer_events", "smooth_factor", "stroke", "transform", "weight"], "pane": "", "pointer_events": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "rotation": true, "scaling": true, "smooth_factor": 1, "stroke": true, "subitems": [], "transform": false, "uniform_scaling": false, "weight": 5}}, "87fc86c5ca764aaca6bcc91fb7ac2880": {"model_module": "jupyter-leaflet", "model_name": "LeafletPolygonModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletPolygonModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletPolygonView", "base": false, "bottom": false, "color": "blue", "dash_array": null, "draggable": false, "fill": true, "fill_color": "transparent", "fill_opacity": 0.4, "line_cap": "round", "line_join": "round", "locations": [[[36.99431407979299, 9.730625221023233], [36.994245473444074, 9.730496465658426], [36.99325067442967, 9.730196036473878], [36.9895114859031, 9.72830762445671], [36.98556627120455, 9.725990027890186], [36.98436551307455, 9.725346251066151], [36.98405674363494, 9.725303332611215], [36.984159666920725, 9.729165993555421], [36.9844341283354, 9.729466422739971], [36.986046569149835, 9.729294748920228], [36.989545791220486, 9.732084448491044], [36.9915354731567, 9.732685306860143], [36.993147763444114, 9.733543675958856], [36.993422192429485, 9.733457839048985], [36.99373092385437, 9.733071572954563], [36.99407395730099, 9.73221320385585], [36.99431407979299, 9.730968568662718]]], "name": "", "no_clip": true, "opacity": 1, "options": ["color", "dash_array", "draggable", "fill", "fill_color", "fill_opacity", "line_cap", "line_join", "no_clip", "opacity", "pointer_events", "smooth_factor", "stroke", "transform", "weight"], "pane": "", "pointer_events": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "rotation": true, "scaling": true, "smooth_factor": 1, "stroke": true, "subitems": [], "transform": false, "uniform_scaling": false, "weight": 5}}, "5b15978ecdb4443f8e782088620c1e8f": {"model_module": "jupyter-leaflet", "model_name": "LeafletPolygonModel", "model_module_version": "^0.19", "state": {"_model_module": "jupyter-leaflet", "_model_module_version": "^0.19", "_model_name": "LeafletPolygonModel", "_view_count": null, "_view_module": "jupyter-leaflet", "_view_module_version": "^0.19", "_view_name": "LeafletPolygonView", "base": false, "bottom": false, "color": "blue", "dash_array": null, "draggable": false, "fill": true, "fill_color": "transparent", "fill_opacity": 0.4, "line_cap": "round", "line_join": "round", "locations": [[[36.992509111214176, 9.745473895912008], [36.991824193275356, 9.74358548389484], [36.99045433889377, 9.741697071877672], [36.98949542614702, 9.742727114796129], [36.98860499634333, 9.74435801608368], [36.988673491313676, 9.744787200633038], [36.99065981862384, 9.747963166298275], [36.992509111214176, 9.745731406641621]]], "name": "", "no_clip": true, "opacity": 1, "options": ["color", "dash_array", "draggable", "fill", "fill_color", "fill_opacity", "line_cap", "line_join", "no_clip", "opacity", "pointer_events", "smooth_factor", "stroke", "transform", "weight"], "pane": "", "pointer_events": "", "popup": null, "popup_max_height": null, "popup_max_width": 300, "popup_min_width": 50, "rotation": true, "scaling": true, "smooth_factor": 1, "stroke": true, "subitems": [], "transform": false, "uniform_scaling": false, "weight": 5}}}}}, "cells": [{"cell_type": "code", "source": ["%pip install segment-geospatial"], "metadata": {"id": "f66LLeLB3PuU", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "972d22cd-d7b6-4f2d-e033-defe80a1b120"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting segment-geospatial\n", "  Downloading segment_geospatial-0.12.2-py2.py3-none-any.whl.metadata (11 kB)\n", "Collecting fiona (from segment-geospatial)\n", "  Downloading fiona-1.10.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (56 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.6/56.6 kB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: gdown in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (5.2.0)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (1.0.1)\n", "Requirement already satisfied: huggingface-hub in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (0.24.7)\n", "Collecting ipympl (from segment-geospatial)\n", "  Downloading ipympl-0.9.4-py3-none-any.whl.metadata (8.7 kB)\n", "Collecting leafmap (from segment-geospatial)\n", "  Downloading leafmap-0.38.8-py2.py3-none-any.whl.metadata (16 kB)\n", "Collecting localtileserver (from segment-geospatial)\n", "  Downloading localtileserver-0.10.4-py3-none-any.whl.metadata (5.2 kB)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (3.7.1)\n", "Requirement already satisfied: opencv-python-headless in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (*********)\n", "Collecting patool (from segment-geospatial)\n", "  Downloading patool-3.0.2-py2.py3-none-any.whl.metadata (4.3 kB)\n", "Requirement already satisfied: pycocotools in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (2.0.8)\n", "Requirement already satisfied: pyproj in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (3.7.0)\n", "Collecting rasterio (from segment-geospatial)\n", "  Downloading rasterio-1.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.1 kB)\n", "Collecting rioxarray (from segment-geospatial)\n", "  Downloading rioxarray-0.17.0-py3-none-any.whl.metadata (5.4 kB)\n", "Collecting sam2 (from segment-geospatial)\n", "  Downloading sam2-0.4.1.tar.gz (75 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m75.3/75.3 kB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: scikit-image in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (0.24.0)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (1.5.2)\n", "Collecting segment-anything-hq (from segment-geospatial)\n", "  Downloading segment_anything_hq-0.3-py3-none-any.whl.metadata (17 kB)\n", "Collecting segment-anything-py (from segment-geospatial)\n", "  Downloading segment_anything_py-1.0.1-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: timm in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (1.0.11)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (4.66.5)\n", "Requirement already satisfied: xarray in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (2024.9.0)\n", "Requirement already satisfied: xyzservices in /usr/local/lib/python3.10/dist-packages (from segment-geospatial) (2024.9.0)\n", "Requirement already satisfied: attrs>=19.2.0 in /usr/local/lib/python3.10/dist-packages (from fiona->segment-geospatial) (24.2.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from fiona->segment-geospatial) (2024.8.30)\n", "Requirement already satisfied: click~=8.0 in /usr/local/lib/python3.10/dist-packages (from fiona->segment-geospatial) (8.1.7)\n", "Collecting click-plugins>=1.0 (from fiona->segment-geospatial)\n", "  Downloading click_plugins-1.1.1-py2.py3-none-any.whl.metadata (6.4 kB)\n", "Collecting cligj>=0.5 (from fiona->segment-geospatial)\n", "  Downloading cligj-0.7.2-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from gdown->segment-geospatial) (4.12.3)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from gdown->segment-geospatial) (3.16.1)\n", "Requirement already satisfied: requests[socks] in /usr/local/lib/python3.10/dist-packages (from gdown->segment-geospatial) (2.32.3)\n", "Requirement already satisfied: numpy>=1.22 in /usr/local/lib/python3.10/dist-packages (from geopandas->segment-geospatial) (1.26.4)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.10/dist-packages (from geopandas->segment-geospatial) (0.10.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from geopandas->segment-geospatial) (24.1)\n", "Requirement already satisfied: pandas>=1.4.0 in /usr/local/lib/python3.10/dist-packages (from geopandas->segment-geospatial) (2.2.2)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from geopandas->segment-geospatial) (2.0.6)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->segment-geospatial) (2024.6.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->segment-geospatial) (6.0.2)\n", "Requirement already satisfied: typing-extensions>=******* in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->segment-geospatial) (4.12.2)\n", "Requirement already satisfied: ipython-genutils in /usr/local/lib/python3.10/dist-packages (from ipympl->segment-geospatial) (0.2.0)\n", "Requirement already satisfied: ipython<9 in /usr/local/lib/python3.10/dist-packages (from ipympl->segment-geospatial) (7.34.0)\n", "Requirement already satisfied: ipywidgets<9,>=7.6.0 in /usr/local/lib/python3.10/dist-packages (from ipympl->segment-geospatial) (7.7.1)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.10/dist-packages (from ipympl->segment-geospatial) (10.4.0)\n", "Requirement already satisfied: traitlets<6 in /usr/local/lib/python3.10/dist-packages (from ipympl->segment-geospatial) (5.7.1)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib->segment-geospatial) (1.3.0)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib->segment-geospatial) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib->segment-geospatial) (4.54.1)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib->segment-geospatial) (1.4.7)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib->segment-geospatial) (3.2.0)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.10/dist-packages (from matplotlib->segment-geospatial) (2.8.2)\n", "Collecting anywidget>=0.9.13 (from leafmap->segment-geospatial)\n", "  Downloading anywidget-0.9.13-py3-none-any.whl.metadata (7.2 kB)\n", "Requirement already satisfied: bqplot>=0.12.43 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (0.12.43)\n", "Requirement already satisfied: colour>=0.1.5 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (0.1.5)\n", "Requirement already satisfied: folium>=0.14.0 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (0.17.0)\n", "Collecting geojson>=3.1.0 (from leafmap->segment-geospatial)\n", "  Downloading geojson-3.1.0-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: ipyevents>=2.0.2 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (2.0.2)\n", "Requirement already satisfied: ipyfilechooser>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (0.6.0)\n", "Requirement already satisfied: ipyleaflet>=0.18.2 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (0.19.2)\n", "Collecting ipyvuetify>=1.9.4 (from leafmap->segment-geospatial)\n", "  Downloading ipyvuetify-1.10.0-py2.py3-none-any.whl.metadata (7.5 kB)\n", "Requirement already satisfied: plotly>=5.15.0 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (5.24.1)\n", "Requirement already satisfied: psutil>=5.9.5 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (5.9.5)\n", "Requirement already satisfied: pyshp>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (2.3.1)\n", "Collecting pystac-client>=0.8.2 (from leafmap->segment-geospatial)\n", "  Downloading pystac_client-0.8.5-py3-none-any.whl.metadata (5.1 kB)\n", "Requirement already satisfied: python-box>=7.2.0 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (7.2.0)\n", "Requirement already satisfied: scooby>=0.10.0 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (0.10.0)\n", "Requirement already satisfied: setuptools>=70.0.0 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (75.1.0)\n", "Collecting whiteboxgui>=2.3.0 (from leafmap->segment-geospatial)\n", "  Downloading whiteboxgui-2.3.0-py2.py3-none-any.whl.metadata (5.7 kB)\n", "Requirement already satisfied: duckdb>=0.10.3 in /usr/local/lib/python3.10/dist-packages (from leafmap->segment-geospatial) (1.1.2)\n", "Requirement already satisfied: flask<4,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from localtileserver->segment-geospatial) (2.2.5)\n", "Collecting Flask-Caching (from localtileserver->segment-geospatial)\n", "  Downloading Flask_Caching-2.3.0-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting flask-cors (from localtileserver->segment-geospatial)\n", "  Downloading Flask_Cors-5.0.0-py2.py3-none-any.whl.metadata (5.5 kB)\n", "Collecting flask-restx>=1.3.0 (from localtileserver->segment-geospatial)\n", "  Downloading flask_restx-1.3.0-py2.py3-none-any.whl.metadata (9.3 kB)\n", "Collecting rio-tiler (from localtileserver->segment-geospatial)\n", "  Downloading rio_tiler-7.0.1-py3-none-any.whl.metadata (12 kB)\n", "Collecting rio-cogeo (from localtileserver->segment-geospatial)\n", "  Downloading rio_cogeo-5.3.6-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting server-thread (from localtileserver->segment-geospatial)\n", "  Downloading server_thread-0.2.0-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: werkzeug in /usr/local/lib/python3.10/dist-packages (from localtileserver->segment-geospatial) (3.0.4)\n", "Collecting affine (from rasterio->segment-geospatial)\n", "  Downloading affine-2.4.0-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: torch>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from sam2->segment-geospatial) (2.5.0+cu121)\n", "Requirement already satisfied: torchvision>=0.18.1 in /usr/local/lib/python3.10/dist-packages (from sam2->segment-geospatial) (0.20.0+cu121)\n", "Collecting hydra-core>=1.3.2 (from sam2->segment-geospatial)\n", "  Downloading hydra_core-1.3.2-py3-none-any.whl.metadata (5.5 kB)\n", "Collecting iopath>=0.1.10 (from sam2->segment-geospatial)\n", "  Downloading iopath-0.1.10.tar.gz (42 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.2/42.2 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: scipy>=1.9 in /usr/local/lib/python3.10/dist-packages (from scikit-image->segment-geospatial) (1.13.1)\n", "Requirement already satisfied: networkx>=2.8 in /usr/local/lib/python3.10/dist-packages (from scikit-image->segment-geospatial) (3.4.2)\n", "Requirement already satisfied: imageio>=2.33 in /usr/local/lib/python3.10/dist-packages (from scikit-image->segment-geospatial) (2.35.1)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /usr/local/lib/python3.10/dist-packages (from scikit-image->segment-geospatial) (2024.9.20)\n", "Requirement already satisfied: lazy-loader>=0.4 in /usr/local/lib/python3.10/dist-packages (from scikit-image->segment-geospatial) (0.4)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->segment-geospatial) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->segment-geospatial) (3.5.0)\n", "Requirement already satisfied: safetensors in /usr/local/lib/python3.10/dist-packages (from timm->segment-geospatial) (0.4.5)\n", "Collecting psygnal>=0.8.1 (from anywidget>=0.9.13->leafmap->segment-geospatial)\n", "  Downloading psygnal-0.11.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.2 kB)\n", "Requirement already satisfied: traittypes>=0.0.6 in /usr/local/lib/python3.10/dist-packages (from bqplot>=0.12.43->leafmap->segment-geospatial) (0.2.1)\n", "Requirement already satisfied: Jinja2>=3.0 in /usr/local/lib/python3.10/dist-packages (from flask<4,>=2.0.0->localtileserver->segment-geospatial) (3.1.4)\n", "Requirement already satisfied: itsdangerous>=2.0 in /usr/local/lib/python3.10/dist-packages (from flask<4,>=2.0.0->localtileserver->segment-geospatial) (2.2.0)\n", "Collecting aniso8601>=0.82 (from flask-restx>=1.3.0->localtileserver->segment-geospatial)\n", "  Downloading aniso8601-9.0.1-py2.py3-none-any.whl.metadata (23 kB)\n", "Requirement already satisfied: jsonschema in /usr/local/lib/python3.10/dist-packages (from flask-restx>=1.3.0->localtileserver->segment-geospatial) (4.23.0)\n", "Requirement already satisfied: pytz in /usr/local/lib/python3.10/dist-packages (from flask-restx>=1.3.0->localtileserver->segment-geospatial) (2024.2)\n", "Requirement already satisfied: importlib-resources in /usr/local/lib/python3.10/dist-packages (from flask-restx>=1.3.0->localtileserver->segment-geospatial) (6.4.5)\n", "Requirement already satisfied: branca>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from folium>=0.14.0->leafmap->segment-geospatial) (0.8.0)\n", "Collecting omegaconf<2.4,>=2.2 (from hydra-core>=1.3.2->sam2->segment-geospatial)\n", "  Downloading omegaconf-2.3.0-py3-none-any.whl.metadata (3.9 kB)\n", "Collecting antlr4-python3-runtime==4.9.* (from hydra-core>=1.3.2->sam2->segment-geospatial)\n", "  Downloading antlr4-python3-runtime-4.9.3.tar.gz (117 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m117.0/117.0 kB\u001b[0m \u001b[31m11.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting portalocker (from iopath>=0.1.10->sam2->segment-geospatial)\n", "  Downloading portalocker-2.10.1-py3-none-any.whl.metadata (8.5 kB)\n", "Requirement already satisfied: jupyter-leaflet<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from ipyleaflet>=0.18.2->leafmap->segment-geospatial) (0.19.2)\n", "Collecting jedi>=0.16 (from ipython<9->ipympl->segment-geospatial)\n", "  Downloading jedi-0.19.1-py2.py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (4.4.2)\n", "Requirement already satisfied: pickleshare in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (0.7.5)\n", "Requirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (3.0.48)\n", "Requirement already satisfied: pygments in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (2.18.0)\n", "Requirement already satisfied: backcall in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (0.2.0)\n", "Requirement already satisfied: matplotlib-inline in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (0.1.7)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl->segment-geospatial) (4.9.0)\n", "Collecting ipyvue<2,>=1.7 (from ipyvuetify>=1.9.4->leafmap->segment-geospatial)\n", "  Downloading ipyvue-1.11.1-py2.py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: ipykernel>=4.5.1 in /usr/local/lib/python3.10/dist-packages (from ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (5.5.6)\n", "Requirement already satisfied: widgetsnbextension~=3.6.0 in /usr/local/lib/python3.10/dist-packages (from ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (3.6.10)\n", "Requirement already satisfied: jupyterlab-widgets>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (3.0.13)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.10/dist-packages (from pandas>=1.4.0->geopandas->segment-geospatial) (2024.2)\n", "Requirement already satisfied: tenacity>=6.2.0 in /usr/local/lib/python3.10/dist-packages (from plotly>=5.15.0->leafmap->segment-geospatial) (9.0.0)\n", "Collecting pystac>=1.10.0 (from pystac[validation]>=1.10.0->pystac-client>=0.8.2->leafmap->segment-geospatial)\n", "  Downloading pystac-1.11.0-py3-none-any.whl.metadata (4.5 kB)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.7->matplotlib->segment-geospatial) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests[socks]->gdown->segment-geospatial) (3.4.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests[socks]->gdown->segment-geospatial) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests[socks]->gdown->segment-geospatial) (2.2.3)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.10/dist-packages (from torch>=2.3.1->sam2->segment-geospatial) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy==1.13.1->torch>=2.3.1->sam2->segment-geospatial) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /usr/local/lib/python3.10/dist-packages (from werkzeug->localtileserver->segment-geospatial) (3.0.2)\n", "Requirement already satisfied: ipytree in /usr/local/lib/python3.10/dist-packages (from whiteboxgui>=2.3.0->leafmap->segment-geospatial) (0.2.2)\n", "Collecting whitebox (from whiteboxgui>=2.3.0->leafmap->segment-geospatial)\n", "  Downloading whitebox-2.3.5-py2.py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->gdown->segment-geospatial) (2.6)\n", "Collecting cachelib<0.10.0,>=0.9.0 (from Flask-Caching->localtileserver->segment-geospatial)\n", "  Downloading cachelib-0.9.0-py3-none-any.whl.metadata (1.9 kB)\n", "Requirement already satisfied: PySocks!=1.5.7,>=1.5.6 in /usr/local/lib/python3.10/dist-packages (from requests[socks]->gdown->segment-geospatial) (1.7.1)\n", "Collecting morecantile<6.0,>=5.0 (from rio-cogeo->localtileserver->segment-geospatial)\n", "  Downloading morecantile-5.4.2-py3-none-any.whl.metadata (7.0 kB)\n", "Requirement already satisfied: pydantic~=2.0 in /usr/local/lib/python3.10/dist-packages (from rio-cogeo->localtileserver->segment-geospatial) (2.9.2)\n", "Requirement already satisfied: cachetools in /usr/local/lib/python3.10/dist-packages (from rio-tiler->localtileserver->segment-geospatial) (5.5.0)\n", "Collecting color-operations (from rio-tiler->localtileserver->segment-geospatial)\n", "  Downloading color_operations-0.1.6-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.0 kB)\n", "Collecting httpx (from rio-tiler->localtileserver->segment-geospatial)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: numexpr in /usr/local/lib/python3.10/dist-packages (from rio-tiler->localtileserver->segment-geospatial) (2.10.1)\n", "Collecting uvicorn (from server-thread->localtileserver->segment-geospatial)\n", "  Downloading uvicorn-0.32.0-py3-none-any.whl.metadata (6.6 kB)\n", "Requirement already satisfied: jupyter-client in /usr/local/lib/python3.10/dist-packages (from ipykernel>=4.5.1->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (6.1.12)\n", "Requirement already satisfied: tornado>=4.2 in /usr/local/lib/python3.10/dist-packages (from ipykernel>=4.5.1->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (6.3.3)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.3 in /usr/local/lib/python3.10/dist-packages (from jedi>=0.16->ipython<9->ipympl->segment-geospatial) (0.8.4)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.10/dist-packages (from pexpect>4.3->ipython<9->ipympl->segment-geospatial) (0.7.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.10/dist-packages (from prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0->ipython<9->ipympl->segment-geospatial) (0.2.13)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic~=2.0->rio-cogeo->localtileserver->segment-geospatial) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.4 in /usr/local/lib/python3.10/dist-packages (from pydantic~=2.0->rio-cogeo->localtileserver->segment-geospatial) (2.23.4)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.10/dist-packages (from jsonschema->flask-restx>=1.3.0->localtileserver->segment-geospatial) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.10/dist-packages (from jsonschema->flask-restx>=1.3.0->localtileserver->segment-geospatial) (0.35.1)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.10/dist-packages (from jsonschema->flask-restx>=1.3.0->localtileserver->segment-geospatial) (0.20.0)\n", "Requirement already satisfied: notebook>=4.4.1 in /usr/local/lib/python3.10/dist-packages (from widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (6.5.5)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx->rio-tiler->localtileserver->segment-geospatial) (3.7.1)\n", "Collecting httpcore==1.* (from httpx->rio-tiler->localtileserver->segment-geospatial)\n", "  Downloading httpcore-1.0.6-py3-none-any.whl.metadata (21 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx->rio-tiler->localtileserver->segment-geospatial) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx->rio-tiler->localtileserver->segment-geospatial)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Requirement already satisfied: pyzmq<25,>=17 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (24.0.1)\n", "Requirement already satisfied: argon2-cffi in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (23.1.0)\n", "Requirement already satisfied: jupyter-core>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (5.7.2)\n", "Requirement already satisfied: nbformat in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (5.10.4)\n", "Requirement already satisfied: nbconvert>=5 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (6.5.4)\n", "Requirement already satisfied: nest-asyncio>=1.5 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.6.0)\n", "Requirement already satisfied: Send2Trash>=1.8.0 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.8.3)\n", "Requirement already satisfied: terminado>=0.8.3 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.18.1)\n", "Requirement already satisfied: prometheus-client in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.21.0)\n", "Requirement already satisfied: nbclassic>=0.4.7 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.1.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx->rio-tiler->localtileserver->segment-geospatial) (1.2.2)\n", "Requirement already satisfied: platformdirs>=2.5 in /usr/local/lib/python3.10/dist-packages (from jupyter-core>=4.6.1->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (4.3.6)\n", "Requirement already satisfied: notebook-shim>=0.2.3 in /usr/local/lib/python3.10/dist-packages (from nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.2.4)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (4.9.4)\n", "Requirement already satisfied: bleach in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (6.1.0)\n", "Requirement already satisfied: defusedxml in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.7.1)\n", "Requirement already satisfied: entrypoints>=0.2.2 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.4)\n", "Requirement already satisfied: jupyterlab-pygments in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.3.0)\n", "Requirement already satisfied: mistune<2,>=0.8.1 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.8.4)\n", "Requirement already satisfied: nbclient>=0.5.0 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.10.0)\n", "Requirement already satisfied: pandocfilters>=1.4.1 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.5.1)\n", "Requirement already satisfied: tinycss2 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.3.0)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /usr/local/lib/python3.10/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (2.20.0)\n", "Requirement already satisfied: argon2-cffi-bindings in /usr/local/lib/python3.10/dist-packages (from argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (21.2.0)\n", "Requirement already satisfied: jupyter-server<3,>=1.8 in /usr/local/lib/python3.10/dist-packages (from notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.24.0)\n", "Requirement already satisfied: cffi>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.17.1)\n", "Requirement already satisfied: webencodings in /usr/local/lib/python3.10/dist-packages (from bleach->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (0.5.1)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (2.22)\n", "Requirement already satisfied: websocket-client in /usr/local/lib/python3.10/dist-packages (from jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl->segment-geospatial) (1.8.0)\n", "Downloading segment_geospatial-0.12.2-py2.py3-none-any.whl (73 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m73.6/73.6 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fiona-1.10.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (17.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m17.3/17.3 MB\u001b[0m \u001b[31m98.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ipympl-0.9.4-py3-none-any.whl (516 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m516.3/516.3 kB\u001b[0m \u001b[31m39.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading leafmap-0.38.8-py2.py3-none-any.whl (488 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m488.2/488.2 kB\u001b[0m \u001b[31m41.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading localtileserver-0.10.4-py3-none-any.whl (17.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m17.1/17.1 MB\u001b[0m \u001b[31m72.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading patool-3.0.2-py2.py3-none-any.whl (98 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m98.1/98.1 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading rasterio-1.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (22.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22.2/22.2 MB\u001b[0m \u001b[31m32.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading rioxarray-0.17.0-py3-none-any.whl (61 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.0/61.0 kB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading segment_anything_hq-0.3-py3-none-any.whl (52 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m53.0/53.0 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading segment_anything_py-1.0.1-py3-none-any.whl (40 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m40.5/40.5 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading anywidget-0.9.13-py3-none-any.whl (213 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m213.7/213.7 kB\u001b[0m \u001b[31m22.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading click_plugins-1.1.1-py2.py3-none-any.whl (7.5 kB)\n", "Downloading cligj-0.7.2-py3-none-any.whl (7.1 kB)\n", "Downloading flask_restx-1.3.0-py2.py3-none-any.whl (2.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.8/2.8 MB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading geojson-3.1.0-py3-none-any.whl (15 kB)\n", "Downloading hydra_core-1.3.2-py3-none-any.whl (154 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m154.5/154.5 kB\u001b[0m \u001b[31m15.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ipyvuetify-1.10.0-py2.py3-none-any.whl (6.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.1/6.1 MB\u001b[0m \u001b[31m110.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pystac_client-0.8.5-py3-none-any.whl (41 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.9/41.9 kB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading whiteboxgui-2.3.0-py2.py3-none-any.whl (108 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m108.6/108.6 kB\u001b[0m \u001b[31m12.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading affine-2.4.0-py3-none-any.whl (15 kB)\n", "Downloading Flask_Caching-2.3.0-py3-none-any.whl (28 kB)\n", "Downloading Flask_Cors-5.0.0-py2.py3-none-any.whl (14 kB)\n", "Downloading rio_cogeo-5.3.6-py3-none-any.whl (20 kB)\n", "Downloading rio_tiler-7.0.1-py3-none-any.whl (264 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m264.4/264.4 kB\u001b[0m \u001b[31m24.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading server_thread-0.2.0-py3-none-any.whl (8.5 kB)\n", "Downloading aniso8601-9.0.1-py2.py3-none-any.whl (52 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.8/52.8 kB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading cachelib-0.9.0-py3-none-any.whl (15 kB)\n", "Downloading ipyvue-1.11.1-py2.py3-none-any.whl (2.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.7/2.7 MB\u001b[0m \u001b[31m95.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jedi-0.19.1-py2.py3-none-any.whl (1.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m80.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading morecantile-5.4.2-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading omegaconf-2.3.0-py3-none-any.whl (79 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.5/79.5 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading psygnal-0.11.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (727 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m727.4/727.4 kB\u001b[0m \u001b[31m53.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pystac-1.11.0-py3-none-any.whl (183 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m183.9/183.9 kB\u001b[0m \u001b[31m20.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading color_operations-0.1.6-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (186 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m186.4/186.4 kB\u001b[0m \u001b[31m20.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.6-py3-none-any.whl (78 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m78.0/78.0 kB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading portalocker-2.10.1-py3-none-any.whl (18 kB)\n", "Downloading uvicorn-0.32.0-py3-none-any.whl (63 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m63.7/63.7 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading whitebox-2.3.5-py2.py3-none-any.whl (72 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.4/72.4 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: sam2, antlr4-python3-runtime, iopath\n", "  Building wheel for sam2 (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for sam2: filename=sam2-0.4.1-cp310-cp310-linux_x86_64.whl size=425006 sha256=0d7cd05d736cd028330c17904d811067a2e1ee2d196d47f56f3601358c7b6619\n", "  Stored in directory: /root/.cache/pip/wheels/f7/24/19/6ae406a45571a7fed8ef81297fc11698486638ac21200cdafa\n", "  Building wheel for antlr4-python3-runtime (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for antlr4-python3-runtime: filename=antlr4_python3_runtime-4.9.3-py3-none-any.whl size=144555 sha256=62ffa9f0bae8e2ddf4828740dc43e34a2768dd8151f2e816175b44e6a43e307c\n", "  Stored in directory: /root/.cache/pip/wheels/12/93/dd/1f6a127edc45659556564c5730f6d4e300888f4bca2d4c5a88\n", "  Building wheel for iopath (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for iopath: filename=iopath-0.1.10-py3-none-any.whl size=31528 sha256=d37cc5f742f642dae7f30c29ce898b638950aa42726de7b55e659fb7a1c0dae3\n", "  Stored in directory: /root/.cache/pip/wheels/9a/a3/b6/ac0fcd1b4ed5cfeb3db92e6a0e476cfd48ed0df92b91080c1d\n", "Successfully built sam2 antlr4-python3-runtime iopath\n", "Installing collected packages: antlr4-python3-runtime, aniso8601, whitebox, psygnal, portalocker, patool, omegaconf, jedi, h11, geojson, color-operations, cligj, click-plugins, cachelib, affine, uvicorn, rasterio, pystac, iopath, hydra-core, httpcore, fiona, server-thread, morecantile, httpx, flask-cors, Flask-Caching, segment-anything-py, segment-anything-hq, sam2, rioxarray, rio-tiler, rio-cogeo, flask-restx, pystac-client, localtileserver, ipyvue, ipympl, anywidget, whiteboxgui, ipyvuetify, leafmap, segment-geospatial\n", "Successfully installed Flask-Caching-2.3.0 affine-2.4.0 aniso8601-9.0.1 antlr4-python3-runtime-4.9.3 anywidget-0.9.13 cachelib-0.9.0 click-plugins-1.1.1 cligj-0.7.2 color-operations-0.1.6 fiona-1.10.1 flask-cors-5.0.0 flask-restx-1.3.0 geojson-3.1.0 h11-0.14.0 httpcore-1.0.6 httpx-0.27.2 hydra-core-1.3.2 iopath-0.1.10 ipympl-0.9.4 ipyvue-1.11.1 ipyvuetify-1.10.0 jedi-0.19.1 leafmap-0.38.8 localtileserver-0.10.4 morecantile-5.4.2 omegaconf-2.3.0 patool-3.0.2 portalocker-2.10.1 psygnal-0.11.1 pystac-1.11.0 pystac-client-0.8.5 rasterio-1.4.1 rio-cogeo-5.3.6 rio-tiler-7.0.1 rioxarray-0.17.0 sam2-0.4.1 segment-anything-hq-0.3 segment-anything-py-1.0.1 segment-geospatial-0.12.2 server-thread-0.2.0 uvicorn-0.32.0 whitebox-2.3.5 whiteboxgui-2.3.0\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["pydevd_plugins"]}, "id": "d967eec646d74d7a92f43e00d728cf0e"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["\n", "using_colab = True"], "metadata": {"id": "X0U9QAiIR4zM"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["if using_colab:\n", "    import torch\n", "    import torchvision\n", "    print(\"PyTorch version:\", torch.__version__)\n", "    print(\"Torchvision version:\", torchvision.__version__)\n", "    print(\"CUDA is available:\", torch.cuda.is_available())\n", "    import sys\n", "    !{sys.executable} -m pip install opencv-python matplotlib\n", "    !{sys.executable} -m pip install 'git+https://github.com/facebookresearch/segment-anything.git'\n", "\n", "\n", "    !wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"], "metadata": {"id": "15LHV6eUR9qP", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4b801889-ad08-4968-da70-3f02657fcf03"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["PyTorch version: 2.5.0+cu121\n", "Torchvision version: 0.20.0+cu121\n", "CUDA is available: True\n", "Requirement already satisfied: opencv-python in /usr/local/lib/python3.10/dist-packages (*********)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.10/dist-packages (3.7.1)\n", "Requirement already satisfied: numpy>=1.21.2 in /usr/local/lib/python3.10/dist-packages (from opencv-python) (1.26.4)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.3.0)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (4.54.1)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (1.4.7)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (24.1)\n", "Requirement already satisfied: pillow>=6.2.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (10.4.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (3.2.0)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.10/dist-packages (from matplotlib) (2.8.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.7->matplotlib) (1.16.0)\n", "Collecting git+https://github.com/facebookresearch/segment-anything.git\n", "  Cloning https://github.com/facebookresearch/segment-anything.git to /tmp/pip-req-build-3fc34dml\n", "  Running command git clone --filter=blob:none --quiet https://github.com/facebookresearch/segment-anything.git /tmp/pip-req-build-3fc34dml\n", "  Resolved https://github.com/facebookresearch/segment-anything.git to commit dca509fe793f601edb92606367a655c15ac00fdf\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Building wheels for collected packages: segment_anything\n", "  Building wheel for segment_anything (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for segment_anything: filename=segment_anything-1.0-py3-none-any.whl size=36592 sha256=1b1177acb412a40853c283fbfcbb84bc3176430ec49f9229d0b608f820218bca\n", "  Stored in directory: /tmp/pip-ephem-wheel-cache-f9cxoqaf/wheels/10/cf/59/9ccb2f0a1bcc81d4fbd0e501680b5d088d690c6cfbc02dc99d\n", "Successfully built segment_anything\n", "Installing collected packages: segment_anything\n", "Successfully installed segment_anything-1.0\n", "--2024-10-24 19:40:49--  https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth\n", "Resolving dl.fbaipublicfiles.com (dl.fbaipublicfiles.com)... ***************, **************, ***************, ...\n", "Connecting to dl.fbaipublicfiles.com (dl.fbaipublicfiles.com)|***************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 2564550879 (2.4G) [binary/octet-stream]\n", "Saving to: ‘sam_vit_h_4b8939.pth’\n", "\n", "sam_vit_h_4b8939.pt 100%[===================>]   2.39G  90.8MB/s    in 12s     \n", "\n", "2024-10-24 19:41:01 (202 MB/s) - ‘sam_vit_h_4b8939.pth’ saved [2564550879/2564550879]\n", "\n"]}]}, {"cell_type": "code", "source": ["import numpy as np\n", "import torch\n", "import matplotlib.pyplot as plt\n", "import cv2"], "metadata": {"id": "9vDEO8MmR9sV"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["def show_mask(mask, ax, random_color=False):\n", "    if random_color:\n", "        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)\n", "    else:\n", "        color = np.array([30/255, 144/255, 255/255, 0.6])\n", "    h, w = mask.shape[-2:]\n", "    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)\n", "    ax.imshow(mask_image)\n", "\n", "def show_points(coords, labels, ax, marker_size=375):\n", "    pos_points = coords[labels==1]\n", "    neg_points = coords[labels==0]\n", "    ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)\n", "    ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)\n", "\n", "def show_box(box, ax):\n", "    x0, y0 = box[0], box[1]\n", "    w, h = box[2] - box[0], box[3] - box[1]\n", "    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))"], "metadata": {"id": "1WxKBpW4R9ut"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import sys\n", "sys.path.append(\"..\")\n", "from segment_anything import sam_model_registry, SamPredictor\n", "\n", "sam_checkpoint = \"sam_vit_h_4b8939.pth\"\n", "model_type = \"vit_h\"\n", "\n", "device = \"cuda\"\n", "\n", "sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)\n", "sam.to(device=device)\n", "\n", "predictor = SamPredictor(sam)"], "metadata": {"id": "wjSUtE-sR9wi", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "a4bb232c-fb97-498c-9d34-e6363fcb2888"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/segment_anything/build_sam.py:105: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  state_dict = torch.load(f)\n"]}]}, {"cell_type": "code", "source": ["from ipyleaflet import Map, Polygon, basemaps\n", "import rasterio\n", "from pyproj import Proj, transform\n", "import json\n", "import cv2\n", "import numpy as np\n", "import leafmap\n", "from PIL import Image\n", "from samgeo import SamGeo, tms_to_geotiff\n", "from skimage.measure import approximate_polygon\n", "\n", "\n", "\n", "\n", "# Create a Leafmap\n", "m = leafmap.Map(center=[37.000260, 9.721093], zoom=16, height=\"800px\")\n", "m.add_basemap(\"SATELLITE\")\n", "\n", "# Global list to store the coordinates of clicked points\n", "clicked_points = []\n", "drawn_polygons = []\n", "\n", "# Function to convert latitude and longitude to pixel coordinates\n", "def latlon_to_pixel(lat, lon, image_width, image_height, min_lat, max_lat, min_lon, max_lon):\n", "    x = int(((lon - min_lon) / (max_lon - min_lon)) * image_width)\n", "    y = int(((max_lat - lat) / (max_lat - min_lat)) * image_height)\n", "    return x, y\n", "\n", "# Function to perform segmentation\n", "def perform_segmentation(lat, lon, ymin, ymax, xmin, xmax):\n", "    # Load the GeoTIFF image\n", "    image_path = 'satellite.tif'\n", "    with rasterio.open(image_path) as src:\n", "        # Read the first band\n", "        image = src.read(1)\n", "        # Get the image dimensions\n", "        image_width = src.width\n", "        image_height = src.height\n", "        # Get the geographic bounds\n", "        bounds = src.bounds\n", "        min_lon = xmin\n", "        max_lon = xmax\n", "        min_lat = ymin\n", "        max_lat = ymax\n", "        # Get the transformation parameters\n", "        transform_params = src.transform\n", "\n", "    # Convert latitude and longitude to pixel coordinates\n", "    x, y = latlon_to_pixel(lat, lon, image_width, image_height, min_lat, max_lat, min_lon, max_lon)\n", "\n", "    # Assuming predictor is defined elsewhere in your code\n", "    point_coords = np.array([[x, y]])  # Reshape to (1, 2)\n", "    point_labels = np.array([1])  # Assuming label is 1\n", "    masks, scores, logits = predictor.predict(\n", "        point_coords=point_coords,\n", "        point_labels=point_labels,\n", "        multimask_output=True,\n", "    )\n", "    print(\"x_pixel=\", x)\n", "    print(\"y_pixel\", y)\n", "\n", "    # Minimum length for a contour segment to be considered valid\n", "    MIN_CONTOUR_LENGTH = 10\n", "    # Display the segmentation result\n", "    i = 0\n", "\n", "    # Convert mask to suitable datatype\n", "    mask_uint8 = (masks[i] * 255).astype(np.uint8)\n", "\n", "   # Find the contours of the segmented region\n", "    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "    contour_points_list = []\n", "    for contour in contours:\n", "        # Extract coordinates of contour points\n", "        contour_points = contour.squeeze(axis=1)\n", "        # Check if the contour has a certain minimum length\n", "        if len(contour_points) > MIN_CONTOUR_LENGTH:\n", "            # Simplify the contour points using <PERSON><PERSON> algorithm\n", "            simplified_contour = approximate_polygon(contour_points, tolerance=2.0)\n", "            # Convert each contour point to geographic coordinates and append to the list\n", "            contour_coords = []\n", "            for contour_point in simplified_contour:\n", "                x, y = contour_point[0], contour_point[1]\n", "\n", "                # Convert pixel coordinates to geographic coordinates\n", "                lon, lat = transform_params * (x, y)\n", "\n", "                # Convert to EPSG:4326\n", "                lon_4326, lat_4326 = geographic_to_epsg4326(lon, lat)\n", "\n", "                # Append contour point to the list\n", "                contour_coords.append([lat_4326, lon_4326])\n", "\n", "            # Append contour coordinates to the list\n", "            contour_points_list.append(contour_coords)\n", "\n", "    # Save contour points to a file\n", "    save_contour_points(contour_points_list)\n", "\n", "    # Draw the polygon on the map\n", "    polygon = Polygon(\n", "        locations=contour_points_list,\n", "        color='blue',\n", "        fill_color='transparent',\n", "        fill_opacity=0.4\n", "    )\n", "    m.add_layer(polygon)\n", "     # Add the drawn polygon to the list\n", "    drawn_polygons.append(polygon)\n", "\n", "\n", "def geographic_to_epsg4326(lon1, lat1):\n", "    source_crs = Proj(init='epsg:3857')\n", "    target_crs = Proj(init='epsg:4326')\n", "    lon_4326, lat_4326 = transform(source_crs, target_crs, lon1, lat1)\n", "    return lon_4326, lat_4326\n", "\n", "def save_contour_points(contour_points):\n", "    polygon = {\n", "        \"type\": \"Polygon\",\n", "        \"coordinates\": [contour_points]  # Assuming contour_points is a list of coordinates\n", "    }\n", "    with open('contour_points.json', 'w') as file:\n", "        json.dump(polygon, file)\n", "\n", "# Function to display coordinates when clicking on the map\n", "def handle_click(**kwargs):\n", "    global clicked_points\n", "    if kwargs.get('type') == 'click':\n", "        lat, lon = kwargs.get('coordinates')\n", "        zoom_level = int(m.zoom)\n", "        print(f\"Latitude: {lat}, Longitude: {lon}, Zoom Level: {zoom_level}\")\n", "        # Calculate parameters for the polygon\n", "        center_lat = lat\n", "        center_lon = lon\n", "        width = 0.02  # Adjust as needed\n", "        height = 0.02  # Adjust as needed\n", "        # Calculate the xmin, xmax, ymin, and ymax\n", "        xmin = center_lon - (width / 2)\n", "        xmax = center_lon + (width / 2)\n", "        ymin = center_lat - (height / 2)\n", "        ymax = center_lat + (height / 2)\n", "        print(f\"xmin: {xmin}, xmax: {xmax}, ymin: {ymin}, ymax: {ymax}\")\n", "        # Define the vertices of the polygon\n", "        contour_points = [\n", "            [ymin, xmin],  # Lower left\n", "            [ymin, xmax],  # Lower right\n", "            [ymax, xmax],  # Upper right\n", "            [ymax, xmin],  # Upper left\n", "            [ymin, xmin]   # Lower left (closing point)\n", "        ]\n", "\n", "        # Draw the polygon with the contour point list\n", "        polygon = Polygon(\n", "            locations=contour_points,\n", "            color='transparent',\n", "            fill_color='transparent',\n", "            fill_opacity=0,\n", "            opacity=0,\n", "            weight=0,\n", "            transform=True\n", "        )\n", "\n", "        # Export the image\n", "        bbox = [xmin, ymin, xmax, ymax]\n", "        image = \"satellite.tif\"\n", "        tms_to_geotiff(output=image, bbox=bbox, zoom=zoom_level, source=\"Satellite\", overwrite=True)\n", "        print(\"Image exported successfully!\")\n", "        image = cv2.imread('satellite.tif')\n", "        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "        predictor.set_image(image)\n", "\n", "        # Call the segmentation function\n", "        perform_segmentation(center_lat, center_lon,ymin,ymax,xmin,xmax)\n", "\n", "# Add click event listener to the map\n", "m.on_interaction(handle_click)\n", "\n", "# Display the map\n", "m\n"], "metadata": {"id": "MsOUWUy7iItP", "colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["703d411fea8d4263b48e7b9fb30d33d2", "0de9971b5ea3477c841871a111617c95", "cf70a100712346e8b01ccae2463476c9", "9922b327e0a9464aaf37bb3e5ad901e3", "eba2cd297a1a4ba9b4d0a9878bc5ccb9", "aa6874af9b1144f8a31a6d92c9db0d6f", "85e76755514d49798f43acc10b639ddf", "6c042ec960a041b38bb1f37dea650385", "ce0492a0a69147acbde3037f37e648cc", "20311a61e17842e582e6f06b27df88bb", "ddbc7064a1014f45bf27fb9a722cff30", "cfacd7b795df404c9622c72000ba18b9", "db48658cb03d407487d57c995a364324", "8f7245fda210467685a10d87e65e48ad", "f2696dc961db46a2ba3d8cc3fb966f02", "cc2e3e5a72524e34ada89d26864b7ff5", "be87846cfa154bf19d9993d8e99f340f", "6250781fb8a24c7d89fb8a160b1457a5", "3f3304c7c3914153ba0e00fd155df197", "9ca674c0fd37485c91d5466b57e5f047", "a4f310d830324a2eb8c2ab3c8b71b3fd", "6c053b04f1604d4a9827ab72d0aab9e1", "460a23e0402f4b30bdbb0095b7980a43", "08f79026859c4970a85eb7297c735447", "791febb6e3314bad9a03bb6e1cd332c7", "2899e151dfd545c2bc79af3f25257d3d", "7ad6c3ab662b4f9991bfbd25df96b727", "9c914e28a711442b990b1b2d02fb4b19", "1d036b6c051f40ba959b8ee997b12e3e", "690f5108c42545a0904155a3d38af197", "bb093a9a25c043828796d74b08f2e780", "76e06e76657047379b57099d10b81638", "fbf4b8f573024c24a150f3a4609c1dba", "96a24efb32d84fdfb8ef6deb0b9a60a2", "ec2a374a6dd6429593928a629b97fef5", "6751dec0923c4a268252d49f10ffcf2b", "e7d5725d8da846508c532ca6d397f37a", "b206856f65da483db48d96047156928a", "ae8b17844b55407cb59c39a6d042d123", "34182905292a46cf9ebacf6c58953eb4", "87fc86c5ca764aaca6bcc91fb7ac2880", "5b15978ecdb4443f8e782088620c1e8f"]}, "outputId": "ace60789-26d2-4940-f8ab-d51b1bb8c7b5"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Map(center=[37.00026, 9.721093], controls=(ZoomControl(options=['position', 'zoom_in_text', 'zoom_in_title', '…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a4f310d830324a2eb8c2ab3c8b71b3fd"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/2b70e893a8ba7c0f/manager.min.js"}}}}}, {"output_type": "stream", "name": "stdout", "text": ["Latitude: 37.000393469769776, Longitude: 9.720110893249513, Zoom Level: 14\n", "xmin: 9.710110893249514, xmax: 9.730110893249513, ymin: 36.99039346976978, ymax: 37.010393469769774\n", "Downloaded image 1/4\n", "Downloaded image 2/4\n", "Downloaded image 3/4\n", "Downloaded image 4/4\n", "Saving GeoTIFF. Please wait...\n", "Image saved to satellite.tif\n", "Image exported successfully!\n", "x_pixel= 116\n", "y_pixel 146\n", "Latitude: 37.00478035638538, Longitude: 9.714918136596681, Zoom Level: 15\n", "xmin: 9.704918136596682, xmax: 9.724918136596681, ymin: 36.994780356385384, ymax: 37.01478035638538\n", "Downloaded image 1/9\n", "Downloaded image 2/9\n", "Downloaded image 3/9\n", "Downloaded image 4/9\n", "Downloaded image 5/9\n", "Downloaded image 6/9\n", "Downloaded image 7/9\n", "Downloaded image 8/9\n", "Downloaded image 9/9\n", "Saving GeoTIFF. Please wait...\n", "Image saved to satellite.tif\n", "Image exported successfully!\n", "x_pixel= 233\n", "y_pixel 292\n", "Latitude: 37.01084618099084, Longitude: 9.722127914428713, Zoom Level: 15\n", "xmin: 9.712127914428713, xmax: 9.732127914428713, ymin: 37.00084618099084, ymax: 37.02084618099084\n", "Downloaded image 1/8\n", "Downloaded image 2/8\n", "Downloaded image 3/8\n", "Downloaded image 4/8\n", "Downloaded image 5/8\n", "Downloaded image 6/8\n", "Downloaded image 7/8\n", "Downloaded image 8/8\n", "Saving GeoTIFF. Please wait...\n", "Image saved to satellite.tif\n", "Image exported successfully!\n", "x_pixel= 233\n", "y_pixel 292\n", "Latitude: 36.989939321354086, Longitude: 9.720067977905275, Zoom Level: 15\n", "xmin: 9.710067977905275, xmax: 9.730067977905275, ymin: 36.97993932135409, ymax: 36.999939321354084\n", "Downloaded image 1/9\n", "Downloaded image 2/9\n", "Downloaded image 3/9\n", "Downloaded image 4/9\n", "Downloaded image 5/9\n", "Downloaded image 6/9\n", "Downloaded image 7/9\n", "Downloaded image 8/9\n", "Downloaded image 9/9\n", "Saving GeoTIFF. Please wait...\n", "Image saved to satellite.tif\n", "Image exported successfully!\n", "x_pixel= 233\n", "y_pixel 291\n", "Latitude: 36.98952798121379, Longitude: 9.72968101501465, Zoom Level: 15\n", "xmin: 9.71968101501465, xmax: 9.73968101501465, ymin: 36.97952798121379, ymax: 36.99952798121379\n", "Downloaded image 1/9\n", "Downloaded image 2/9\n", "Downloaded image 3/9\n", "Downloaded image 4/9\n", "Downloaded image 5/9\n", "Downloaded image 6/9\n", "Downloaded image 7/9\n", "Downloaded image 8/9\n", "Downloaded image 9/9\n", "Saving GeoTIFF. Please wait...\n", "Image saved to satellite.tif\n", "Image exported successfully!\n", "x_pixel= 233\n", "y_pixel 291\n", "Latitude: 36.990659161246896, Longitude: 9.745001792907717, Zoom Level: 14\n", "xmin: 9.735001792907717, xmax: 9.755001792907716, ymin: 36.9806591612469, ymax: 37.000659161246894\n", "Downloaded image 1/2\n", "Downloaded image 2/2\n", "Saving GeoTIFF. Please wait...\n", "Image saved to satellite.tif\n", "Image exported successfully!\n", "x_pixel= 116\n", "y_pixel 146\n"]}]}]}