# Google Cloud Platform Configuration
GOOGLE_APPLICATION_CREDENTIALS=keyfile.json
GCP_PROJECT=your-gcp-project-id
GCP_LOCATION=us-central1

# SAM Model Configuration
# Option 1: If using a deployed SAM endpoint, provide the endpoint ID
SAM_ENDPOINT_ID=your-sam-endpoint-id

# Option 2: If using ImageSegmentationModel.from_pretrained(), 
# the model name will be tried automatically (sam, segment-anything)

# Optional: Override temp image output directory
# TEMP_IMAGES_DIR=temp_images
